import 'package:flutter/material.dart';
import '../../utils/theme_constants.dart';

class JsonMessageBubble extends StatelessWidget {
  final String jsonText;
  final void Function()? onCopy;
  final void Function()? onDeploy;
  final void Function()? onViewChart;
  final TextSpan Function(String) highlightJson;
  final String Function(String) prettyPrintJson;

  const JsonMessageBubble({
    super.key,
    required this.jsonText,
    required this.onCopy,
    required this.onDeploy,
    required this.onViewChart,
    required this.highlightJson,
    required this.prettyPrintJson,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
        decoration: BoxDecoration(
          color: ThemeConstants.zenBlack,
          borderRadius: BorderRadius.circular(20),
          boxShadow: const [
            BoxShadow(color: Colors.black26, blurRadius: 8, offset: Offset(0, 2)),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header bar
            Container(
              decoration: const BoxDecoration(
                color: Color(0xFF23272f),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(14),
                  topRight: Radius.circular(14),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'json',
                    style: TextStyle(
                      color: Color(0xFFbdbdbd),
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.5,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 18, color: Color(0xFFbdbdbd)),
                    tooltip: 'Copy code',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    onPressed: onCopy,
                  ),
                ],
              ),
            ),
            // Code block area
            Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Color(0xFF181b20),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(14),
                  bottomRight: Radius.circular(14),
                ),
              ),
              padding: const EdgeInsets.fromLTRB(16, 14, 16, 18),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: RichText(
                      text: highlightJson(prettyPrintJson(jsonText)),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        icon: const Icon(Icons.account_tree),
                        label: const Text('View Chart'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF23272f),
                          foregroundColor: const Color(0xFFbdbdbd),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: onViewChart,
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.cloud_upload),
                        label: const Text('Deploy'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF23272f),
                          foregroundColor: const Color(0xFFbdbdbd),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: onDeploy,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
