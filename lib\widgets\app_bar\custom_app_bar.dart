import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/fetch_clients_data/bloc/clients_data_bloc.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/client/client_selector_modal.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  final bool isTransparent;
  final List<Widget>? actions;
  final ValueSetter<int>? onClientSwitched;

  const CustomAppBar({
    super.key,
    this.isTransparent = false,
    this.actions,
    this.onClientSwitched,
  });

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _CustomAppBarState extends State<CustomAppBar> {
  int currentClientIndex = 0;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: widget.isTransparent
          ? Colors.transparent
          : ThemeConstants.backgroundColor,
      title: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          if (authState is AuthAuthenticated) {
            final user = authState.credentialsModel;
            return BlocBuilder<ClientsDataBloc, ClientsDataState>(
              builder: (context, clientsState) {
                if (clientsState is ClientsDataLoaded) {
                  final clientsList = clientsState.clientsList;

                  // Initialize currentClientIndex based on current user clientId
                  final currentIndex = clientsList
                      .indexWhere((client) => client.clientId == user.clientId);
                  if (currentIndex != -1 &&
                      currentClientIndex != currentIndex) {
                    currentClientIndex = currentIndex;
                  }

                  void switchClient(int newIndex) {
                    final clientId = clientsList[newIndex].clientId;
                    context
                        .read<AuthBloc>()
                        .add(ClientChangeEvent(clientId: clientId));
                    setState(() {
                      currentClientIndex = newIndex;
                    });
                    if (widget.onClientSwitched != null) {
                      widget.onClientSwitched!(clientId);
                    }
                  }

                  return AnimatedSwitcher(
                    duration: const Duration(milliseconds: 400),
                    switchInCurve: Curves.easeInOutCubic,
                    switchOutCurve: Curves.easeInOutCubic,
                    transitionBuilder: (child, animation) {
                      final isForward = (child.key as ValueKey?)?.value == clientsList[currentClientIndex].clientId;
                      final rotateAnim = Tween(begin: isForward ? 0.18 : -0.18, end: 0.0).animate(CurvedAnimation(parent: animation, curve: Curves.easeInOutCubic));
                      final fadeAnim = Tween<double>(begin: 0.0, end: 1.0).animate(animation);
                      return AnimatedBuilder(
                        animation: animation,
                        child: child,
                        builder: (context, child) {
                          return Opacity(
                            opacity: fadeAnim.value,
                            child: Transform(
                              alignment: Alignment.centerLeft,
                              transform: Matrix4.identity()
                                ..setEntry(3, 2, 0.001)
                                ..rotateY(rotateAnim.value),
                              child: child,
                            ),
                          );
                        },
                      );
                    },
                    child: Container(
                      key: ValueKey(clientsList[currentClientIndex].clientId),
                      width: 140, // static width
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: ThemeConstants.blue.withOpacity(0.5),
                          width: 1,
                        ),
                        color: ThemeConstants.blue.withOpacity(0.2),
                      ),
                      child: GestureDetector(
                        onTap: () async {
                          final selected = await showModalBottomSheet<int>(
                            context: context,
                            backgroundColor: ThemeConstants.backgroundColor,
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(top: Radius.circular(18)),
                            ),
                            builder: (context) {
                              return ClientSelectorModal(
                                clientsList: clientsList,
                                currentClientIndex: currentClientIndex,
                                onSelected: (int selectedIndex) {
                                  Navigator.pop(context, selectedIndex);
                                },
                              );
                            },
                          );
                          if (selected != null && selected != currentClientIndex) {
                            switchClient(selected);
                          }
                        },
                        onVerticalDragEnd: (details) {
                          if (details.primaryVelocity == null) return;
                          if (details.primaryVelocity! < 0) {
                            // Swipe Up
                            final newIndex = (currentClientIndex + 1) % clientsList.length;
                            switchClient(newIndex);
                          } else if (details.primaryVelocity! > 0) {
                            // Swipe Down
                            final newIndex = (currentClientIndex - 1) < 0
                                ? clientsList.length - 1
                                : currentClientIndex - 1;
                            switchClient(newIndex);
                          }
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.start, // left align
                          children: [
                            SizedBox(
                              width: 12,
                              height: 12,
                              child: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: ThemeConstants.blue,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.blue.withOpacity(0.2),
                                      blurRadius: 4,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                clientsList[currentClientIndex].clientName,
                                key: ValueKey(clientsList[currentClientIndex].clientId),
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: const Color(0xffCDCDCD),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            const SizedBox(width: 6),
                            Icon(Icons.unfold_more, size: 18, color: Color(0xffCDCDCD)),
                          ],
                        ),
                      ),
                    ),
                  );
                } else {
                  return const Text(
                    "Loading...",
                    style: TextStyle(
                      color: Color(0xffCDCDCD),
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }
              },
            );
          } else {
            return const Text(
              "Loading...",
              style: TextStyle(
                color: Color(0xffCDCDCD),
                fontSize: 12,
                fontWeight: FontWeight.w700,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }
        },
      ),
      iconTheme: IconThemeData(color: Color(0xffCDCDCD)),
      actions: widget.actions,
      surfaceTintColor: Colors.transparent,
    );
  }
}
