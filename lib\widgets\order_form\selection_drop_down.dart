import 'package:flutter/material.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

class SelectionDropdown<T> extends StatelessWidget {
  final String hint;
  final T? selectedValue;
  final List<T> items;
  final String Function(T) getItemLabel;
  final void Function(T?) onChanged;

  final TextStyle itemTextStyle;

  final BoxDecoration dropdownDecoration;

  const SelectionDropdown({
    super.key,
    required this.hint,
    required this.selectedValue,
    required this.items,
    required this.getItemLabel,
    required this.onChanged,
    this.itemTextStyle = const TextStyle(
      fontSize: 15,
      color: Color(0xffADADAD),
      fontWeight: FontWeight.w400,
    ),
    this.dropdownDecoration = const BoxDecoration(
      color: Color(0xff353535),
      borderRadius: BorderRadius.all(Radius.circular(10)),
    ),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      //padding: const EdgeInsets.all(2),
      alignment: Alignment.center,
      height: 40,
      width: MediaQuery.of(context).size.width * 0.445,
      decoration: dropdownDecoration,
      child: Center(
        child: DropdownButtonHideUnderline(
          child: DropdownButton2<T>(
            hint: Text(
              hint,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                color: Color(0xffADADAD),
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            isExpanded: true,
            items: items.map((T item) {
              return DropdownMenuItem<T>(
                value: item,
                child: Text(
                  getItemLabel(item),
                  overflow: TextOverflow.ellipsis,
                  style: itemTextStyle,
                ),
              );
            }).toList(),
            value: selectedValue,
            onChanged: onChanged,
            buttonStyleData: const ButtonStyleData(
              padding: EdgeInsets.symmetric(horizontal: 16),
              height: 100,
              width: 400,
            ),
            dropdownStyleData: DropdownStyleData(
              decoration: dropdownDecoration,
              maxHeight: 500,
              width: 200,
            ),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
            ),
          ),
        ),
      ),
    );
  }
}
