import 'package:flutter/material.dart';
import 'package:phoenix/utils/util_functions.dart';

//Indicator
class OrderStatusIndicator extends StatelessWidget {
  const OrderStatusIndicator({
    super.key,
    required this.status,
  });

  final String status;

  /// Get status properties (background color, text color, and formatted text)
  Map<String, dynamic> _getStatusProperties() {
    final lowerStatus = status.toLowerCase();

    final statusMap = {
      'open': {
        'backgroundColor': Color.fromARGB(255, 234, 231, 57).withOpacity(0.25),
        'textColor': Color.fromARGB(255, 234, 231, 57),
        'text': 'Open',
      },
      'in_progress': {
        'backgroundColor': Color.fromARGB(255, 234, 231, 57).withOpacity(0.25),
        'textColor': Color.fromARGB(255, 234, 231, 57),
        'text': 'In Progress',
      },
      'update': {
        'backgroundColor': Color(0xffEA7239).withOpacity(0.25),
        'textColor': Color(0xffEA7239),
        'text': 'Pending',
      },
      'trigger_pending': {
        'backgroundColor': Color(0xff338AFF).withOpacity(0.25),
        'textColor': Color(0xff338AFF),
        'text': 'Trigger pending',
      },
      'stop_los_hit': {
        'backgroundColor': Color(0xff338AFF).withOpacity(0.25),
        'textColor': Color(0xff338AFF),
        'text': 'Stoploss Hit',
      },
      'completed': {
        'backgroundColor': Color(0xff028824).withOpacity(0.25),
        'textColor': Color(0xff028824),
        'text': 'Completed',
      },
      'rejected': {
        'backgroundColor': Color(0xffD64242).withOpacity(0.25),
        'textColor': Color(0xffD64242),
        'text': 'Rejected',
      },
      'cancelled': {
        'backgroundColor': Color(0xffD64242).withOpacity(0.25),
        'textColor': Color(0xffD64242),
        'text': 'Cancelled',
      },
      'trigger_pending_with_error': {
        'backgroundColor': Color(0xffC10707).withOpacity(0.25),
        'textColor': Color(0xffC10707),
        'text': 'Trigger pending with error',
      },
      'error': {
        'backgroundColor': Color(0xffC10707).withOpacity(0.25),
        'textColor': Color(0xffC10707),
        'text': 'Error',
      },
      'pending': {
        'backgroundColor': Color(0xffEA7239).withOpacity(0.25),
        'textColor': Color(0xffEA7239),
        'text': 'Pending',
      },
      

    };

    // Default values for unknown statuses
    return statusMap[lowerStatus] ??
        {
          'backgroundColor': Colors.grey.withOpacity(0.2),
          'textColor': Colors.grey,
          'text': lowerStatus.toCapitalized,
        };
  }

  @override
  Widget build(BuildContext context) {
    final statusProps = _getStatusProperties();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: statusProps['backgroundColor'],
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        statusProps['text'],
        style: TextStyle(
          color: statusProps['textColor'],
          fontSize: 15,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
