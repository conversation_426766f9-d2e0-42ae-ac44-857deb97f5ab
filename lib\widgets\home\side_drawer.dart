import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/utils/theme_constants.dart';

///This is the side menu Drawer we use to
/// Logout
/// Accounts section
/// Settings
///
class SideDrawer extends StatelessWidget {
  const SideDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if(state is AuthUnauthenticated){
          Navigator.popAndPushNamed(context, '/login');
        }
      },
      child: Container(
        width: MediaQuery.of(context).size.width * 0.5,
        decoration: BoxDecoration(
          color: ThemeConstants.bgSideDrawer,
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(10),
            bottomRight: Radius.circular(10),
          ),
          boxShadow: [
            BoxShadow(
              blurRadius: 10,
              color: Color(0xff383838),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(10),
            bottomRight: Radius.circular(10),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(16),
                  height: 120,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Menu',
                      style: TextStyle(
                        fontFamily: 'Roboto',
                        color: Color(0xffCDCDCD),
                        fontSize: 24,
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ),
                ),
                _buildMenuItem(
                  icon: Icons.person_outline,
                  title: 'Profile',
                  onTap: () {
                    Navigator.popAndPushNamed(context, '/account');
                  },
                ),
                SideDrawerMenuItemDivider(),
                _buildMenuItem(
                  icon: Icons.settings_outlined,
                  title: 'Settings',
                  onTap: () {
                    Navigator.popAndPushNamed(context, '/settings');
                  },
                ),
                // SideDrawerMenuItemDivider(),
                // _buildMenuItem(
                //   icon: Icons.help_outline,
                //   title: 'Help',
                //   onTap: () {},
                // ),
                SideDrawerMenuItemDivider(),
                const Spacer(),
                _buildLogoutItem(
                  icon: Icons.logout,
                  title: 'Logout',
                  onTap: () {
                    debugPrint("Logout");
                    context.read<AuthBloc>().add(AuthLogoutEvent());
                  },
                  context: context,
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    required BuildContext context,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () {
            //Show a confirmation dialog
            showDialog(
              context: context, // Make sure you have access to the context
              builder: (BuildContext context) {
                return AlertDialog(
                  title: Text("Confirm Logout"),
                  content: Text("Are you sure you want to log out?"),
                  actions: <Widget>[
                    TextButton(
                      child: Text("Cancel"),
                      onPressed: () {
                        Navigator.of(context).pop(); // Close the dialog
                      },
                    ),
                    TextButton(
                      child: Text("Logout"),
                      onPressed: () {
                        onTap(); // Call the logout function
                      },
                    ),
                  ],
                );
              },
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Roboto',
                    color: Color(0xffCDCDCD),
                    fontSize: 24,
                    fontWeight: FontWeight.w900,
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                ImageIcon(
                  AssetImage("images/logout_icon.png"),
                  size: 24,
                  color: Color(0xffCDCDCD),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: const Color(0xffCDCDCD),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: ThemeConstants.sdTextStyle,
                ),
                const Spacer(),
                const Icon(
                  Icons.arrow_forward,
                  color: Color(0xffCDCDCD),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class SideDrawerMenuItemDivider extends StatelessWidget {
  const SideDrawerMenuItemDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Divider(
      color: Color(0xffCDCDCD),
      indent: 8,
      endIndent: 8,
      height: 1,
    );
  }
}
