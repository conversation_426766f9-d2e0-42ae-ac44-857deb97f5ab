import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/features/commissions_data_map/bloc/commissions_data_map_bloc.dart';
import 'package:phoenix/features/commissions_data_map/model/broker_charge_model.dart';
import 'package:phoenix/features/commissions_data_map/model/exchange_charge_model.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/services/comissions_charges_service.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/animated_icons/spinning_icon_button.dart';
import 'package:phoenix/widgets/order_form/charges_bar/charges_modal.dart';

//Charges bar

class ChargesBar extends StatefulWidget {
  final double? price;
  final int? quantity;
  final SecurityModel? selectedValue;
  final bool isMarket;
  final AnimationController formRefreshController;
  final Function fetchLtpPrice;
  final String? exchange;
  final String? productType;
  final String? broker;
  final String? transactionType;

  const ChargesBar({
    super.key,
    this.price,
    this.quantity,
    this.selectedValue,
    required this.isMarket,
    required this.formRefreshController,
    required this.fetchLtpPrice,
    this.exchange,
    this.productType,
    this.broker,
    this.transactionType,
  });

  @override
  State<ChargesBar> createState() => _ChargesBarState();
}

class _ChargesBarState extends State<ChargesBar> {

  CalculatedCharges? calculatedCharges;

  @override
  void initState() {
    super.initState();
    debugPrint("Charges Bar Init");
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CommissionsDataMapBloc, CommissionsDataMapState>(
      listener: (context, state) {
      },
      builder: (context, state) {
        if (state is CommissionsDataMapLoaded) {
          final product = widget.productType == "MIS" ? "INTRADAY" : "DELIVERY";
          BrokerAccountStrategySelectionBloc
              brokerAccountStrategySelectionBloc =
              BlocProvider.of<BrokerAccountStrategySelectionBloc>(context);
          final brokerAccountStrategySelectionBlocState =
              brokerAccountStrategySelectionBloc.state;

          final type = widget.selectedValue?.instrumentType;

          final instrumentType = (type == "CE" || type == "PE") ? "OPT" : ( type == "FUT" ? "FUT" : "EQ");

          final exchangeKey =
              "${widget.exchange}|$instrumentType|${widget.transactionType}|$product";
          final brokerKey =
              "${brokerAccountStrategySelectionBlocState.selectedBroker?.brokerName}|$instrumentType|$product";
          debugPrint("Exchange Key: $exchangeKey");
          debugPrint("Broker Key: $brokerKey");

          final exchangeCharges =
              state.data['exchangeCharges'][exchangeKey] ?? 0;
          final brokerCharges = state.data['brokerCharges'][brokerKey] ?? 0;
          debugPrint("Exchange Charges: ${exchangeCharges.toString()}");
          debugPrint("Broker Charges: ${brokerCharges.toString()}");

          final turnover = ((widget.price ) ?? 0 ) * (widget.quantity ?? 0);
          
          debugPrint("Turnover: ${turnover.toString()}");
            final calculatedCharges = CommissionChargesService.calculateCharges(
            turnover: turnover,
            brokerModel: brokerCharges !=0 ? brokerCharges : BrokerChargeModel(threshold: 0, brokerageCharges: 0, flatCharges: 0),
            exchangeModel: exchangeCharges !=0 ? exchangeCharges : ExchangeChargeModel(sttCtt: 0, transactionCharges: 0, ipft: 0, sebi: 0, stampCharges: 0, gst: 0),
          );
          this.calculatedCharges = calculatedCharges;

          debugPrint("Calculated Charges: ${this.calculatedCharges!.toString()}");
        }

        return Container(
          height: 32,
          decoration: BoxDecoration(
              color: Color(0xffD9D9D9),
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(10),
              )),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Amount',
                style: TextStyle(
                  color: Colors.black,
                ),
              ),
              BlocBuilder<WebSocketBloc, WebSocketState>(
                builder: (context, state) {
                  if (state is WebSocketStockPriceUpdated && widget.isMarket) {
                    if (state.stockPrice != null) {
                      double total = state.stockPrice! * widget.quantity!;
                      if (state.stockPrice != 0) {
                        return Text(
                          '₹ ${UtilFunctions.formatIndianCurrency(total)}',
                          style: TextStyle(
                            color: Colors.green,
                          ),
                        );
                      }
                    }
                  }

                  if (widget.price != 0 && widget.price != null) {
                    double total = widget.price! * widget.quantity!;
                    return Text(
                      '₹${UtilFunctions.formatIndianCurrency(total)}',
                      style: TextStyle(
                        color: Colors.green,
                      ),
                    );
                  }
                  return Text(' --- ');
                },
              ),
              GestureDetector(
                onTap: () {
                  if (calculatedCharges != null) {
                    ChargesModal.show(
                      context,
                      charges: calculatedCharges!,
                    );
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                  'Charges',
                  style: TextStyle(
                    color: Colors.black,
                  ),
                ),
                const SizedBox(width: 2),
                Icon(
                  Icons.info_outline,
                  color: Colors.black,
                  size: 12,
                ),
                const SizedBox(width: 4),
                if (calculatedCharges != null)
                  Text(
                    ' ₹${UtilFunctions.formatIndianCurrency(calculatedCharges!.finalCharges)}',
                    style: TextStyle(
                      color: Colors.green,
                    ),
                  ),
                  ],
                )
              ),
              
              SpinningIconButton(
                controller: widget.formRefreshController,
                iconData: Icons.refresh,
                onPressed: () {
                  widget.formRefreshController.repeat();
                  widget.formRefreshController.forward(
                    from: widget.formRefreshController.value,
                  );
                  //only refresh if it is market
                  if (widget.selectedValue != null && widget.isMarket) {
                    widget.fetchLtpPrice(widget.selectedValue!.zenId);
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
