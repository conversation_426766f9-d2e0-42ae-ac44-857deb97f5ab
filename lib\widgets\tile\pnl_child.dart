import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/broker_avatar/broker_avatar.dart';

class PnlChild extends StatefulWidget {
  const PnlChild(
      {super.key,
      required this.data,
      this.prices,
      this.filter = 'latest',
      required this.brokerAccountStrategyData});

  final PositionPnL data;
  final double? prices;
  final BrokerAccountStrategyData? brokerAccountStrategyData;
  final String filter;

  @override
  State<PnlChild> createState() => _PnlChildState();
}

class _PnlChildState extends State<PnlChild> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double unRealized = widget.data.unRealized.getByKey(widget.filter)!;
    double unRealizedPercentageChange =
        (unRealized / widget.data.openCost) * 100;

    double realized = widget.data.realized.getByKey(widget.filter)!;

    double dividend = widget.data.dividend.getByKey(widget.filter)!;
    bool isRealizedZero = realized == 0;

    if (widget.filter == 'latest') {
      unRealized = widget.data.unRealized.getByKey(widget.filter)!;
      unRealizedPercentageChange = (unRealized / widget.data.openCost) * 100;
      realized = widget.data.realized.getByKey(widget.filter)!;
      dividend = widget.data.dividend.getByKey(widget.filter)!;
      isRealizedZero = realized == 0;
    } else {
      unRealized = widget.data.unRealized.latest -
          widget.data.unRealized.getByKey(widget.filter)!;
      unRealizedPercentageChange = (unRealized / widget.data.openCost) * 100;
      realized = widget.data.realized.latest -
          widget.data.realized.getByKey(widget.filter)!;
      dividend = widget.data.dividend.latest -
          widget.data.dividend.getByKey(widget.filter)!;
      isRealizedZero = realized == 0;
    }

    //debugPrint(" ${widget.data.tradingSymbol}  ${widget.prices.toString()} ${widget.filter}");

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: ThemeConstants.backgroundColor,
        boxShadow: ThemeConstants.neomorpicShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left Column
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.data.tradingSymbol,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(width: 4),
                  if (widget.data.positionCompositeKey.broker.isNotEmpty)
                    BrokerAvatar(
                      brokerName:
                          widget.brokerAccountStrategyData?.brokerName ?? "N/A",
                    ),
                ],
              ),

              // Strategy
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const ImageIcon(
                    AssetImage("images/tile-generic/strategy.png"),
                    color: Colors.blue,
                    size: 15,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    widget.brokerAccountStrategyData?.strategyName ?? "N/A",
                    style: const TextStyle(
                      color: Color(0xffCDCDCD),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
              

              

              const SizedBox(height: 19),

              // Realized Value
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (!isRealizedZero)
                    Icon(
                      realized >= 0 ? Icons.trending_up : Icons.trending_down,
                      color: realized >= 0
                          ? ThemeConstants.tileGreenColor
                          : ThemeConstants.titleRedColor,
                      size: 16,
                    ),
                  const SizedBox(width: 4),
                  PnlFormatter(value: realized),
                ],
              ),
            ],
          ),

          // Right Column
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              PnlFormatter(value: unRealized),
              PercentageValidator(
                unrealizedPercentage: unRealizedPercentageChange,
              ),
              const SizedBox(height: 28),
              Row(
                children: [
                  const ImageIcon(
                    AssetImage("images/tile-generic/chart_pie.png"),
                    color: Colors.grey,
                    size: 14,
                  ),
                  const SizedBox(width: 2),
                  Text(
                    dividend == 0
                        ? "--"
                        : UtilFunctions.formatIndianCurrency(dividend),
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }

  Text daysPnlFormatter(double daysChange) {
    Color textColor;
    String displayText;
    if (daysChange.isInfinite || daysChange.isNaN) {
      displayText = " ";
      textColor = Colors.grey;
    } else if (daysChange == 0) {
      displayText = " (0.00%)";
      textColor = Colors.grey;
    } else if (daysChange > 0) {
      displayText = " (+${daysChange.toStringAsFixed(2)}%)";
      textColor = const Color(0xFF00FF29);
    } else {
      displayText = " (${daysChange.toStringAsFixed(2)}%)";
      textColor = ThemeConstants.titleRedColor;
    }

    return Text(
      displayText,
      style: TextStyle(
        color: textColor,
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}

class PnlFormatter extends StatelessWidget {
  const PnlFormatter({
    super.key,
    required this.value,
  });

  final double value;

  @override
  Widget build(BuildContext context) {
    Color textColor;
    String displayText;

    if (value.isInfinite || value.isNaN) {
      displayText = "--";
      textColor = ThemeConstants.zenGrey;
    } else if (value == 0) {
      displayText = "--";
      textColor = ThemeConstants.zenGrey;
    } else if (value > 0) {
      displayText = "+${UtilFunctions.formatIndianCurrency(value)}";
      textColor = ThemeConstants.tileGreenColor;
    } else {
      displayText = UtilFunctions.formatIndianCurrency(value);
      textColor = ThemeConstants.titleRedColor;
    }

    return Text(
      displayText,
      style: TextStyle(
        color: textColor,
        fontSize: 15,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}

class PercentageValidator extends StatelessWidget {
  const PercentageValidator({
    super.key,
    required this.unrealizedPercentage,
  });

  final double unrealizedPercentage;

  @override
  Widget build(BuildContext context) {
    Color textColor;
    String displayText;

    if (unrealizedPercentage.isInfinite || unrealizedPercentage.isNaN) {
      displayText = "--";
      textColor = ThemeConstants.zenGrey;
    } else if (unrealizedPercentage == 0) {
      displayText = "--";
      textColor = ThemeConstants.zenGrey;
    } else if (unrealizedPercentage > 0) {
      displayText = "+${unrealizedPercentage.toStringAsFixed(2)}%";
      textColor = ThemeConstants.tileGreenColor;
    } else {
      displayText = "${unrealizedPercentage.toStringAsFixed(2)}%";
      textColor = ThemeConstants.titleRedColor;
    }

    return Text(
      displayText,
      style: TextStyle(
        color: textColor,
        fontSize: 13,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
