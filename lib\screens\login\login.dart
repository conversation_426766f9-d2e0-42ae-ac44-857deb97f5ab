import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/inner_shadow_button.dart';

class Login extends StatelessWidget {
  const Login({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          Navigator.pushReplacementNamed(context, '/home');
        }
      },
      builder: (context, state) {
        // Show loading screen when authentication is in progress
        if (state is AuthLoading) {
          return Scaffold(
            backgroundColor: const Color(0xff24272C),
            body: const Center(
              child: CircularLoader(),
            ),
          );
        }

        // Main login screen
        return Scaffold(
          backgroundColor: const Color(0xff24272C),
          body: Stack(
            children: [
              // Center Logo
              Center(
                child: Image.asset(
                  'images/phoenix-logo.png',
                  width: 200,
                  height: 200,
                ),
              ),
              // "Getting Started" button at the bottom
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Display error message if there is an error state
                    if (state is AuthError)
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10.0),
                        child: Text(
                          state.errorMessage,
                          style: const TextStyle(
                            color: Colors.redAccent,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    const SizedBox(height: 25),
                    InnerShadowButton(
                      action: () async {
                        context.read<AuthBloc>().add(AuthLoginEvent());
                      },
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
