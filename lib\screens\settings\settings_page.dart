import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/screens/settings/widgets/price_stream_indicator.dart';
import 'package:phoenix/screens/settings/widgets/report_error_widget.dart';
import 'package:phoenix/services/logger_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/circular_loader.dart';

import 'widgets/biometric_toggle.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthUnauthenticated) {
          Navigator.pushReplacementNamed(context, '/');
        }
      },
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return const Scaffold(
            backgroundColor: ThemeConstants.backgroundColor,
            body: Center(child: CircularLoader()),
          );
        }

        return Scaffold(
          backgroundColor: ThemeConstants.backgroundColor,
          appBar: AppBar(
            backgroundColor: ThemeConstants.backgroundColor,
            //leading: ImageIcon(AssetImage("images/back_arrow_icon.png"),size: 2,color: const Color(0xffCDCDCD),),
            title: Row(
              children: [
                ImageIcon(
                  AssetImage("images/settings_icon.png"),
                  size: 24,
                  color: const Color(0xffCDCDCD),
                ),
                SizedBox(width: 6),
                Text(
                  "Settings",
                  style: TextStyle(
                    color: const Color(0xffCDCDCD),
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            iconTheme: IconThemeData(color: Color(0xffCDCDCD)),
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 18),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    PriceStreamIndicator(),
                    SizedBox(height: 8),
                    BiometricToggle(),
                    ReportErrorWidget(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
