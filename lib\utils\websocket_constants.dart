abstract class Constants {
  static const String auth0Domain = String.fromEnvironment(
    'AUTH0_DOMAIN',
    defaultValue: 'phoenix-lab.us.auth0.com',
  );

  static const String auth0ClientId = String.fromEnvironment(
    'AUTH0_CLIENT_ID',
    defaultValue: 'VwsIt0Zf8o1japchViIbgPaLhYFJkX3C',
  );

  static const String devHost = "https://api.dev.phoenix.zentropylabs.com";

}

abstract class WebSocketConstants {
  static const String productionUrl = 'wss://prod.example.com';
  static const String stagingUrl = 'wss://staging.example.com';
  static const String localUrl = 'ws://************:8766';

  /* ACTIVE */ static const String protoWSUrl = webSocketProd;

  // 'ws://*************:8095/ws/prices'
  
  static const String webSocketDevPreveen = 'ws://*************:8095/ws/prices';

  static const String webSocketDevIP = 'ws://*************:8095/ws/prices';

  static const String webSocketUatIP = 'ws://*************:8095/ws/prices';


  static const String webSocketDev = "wss://websocket.dev.phoenix.zentropylabs.com/ws/prices";

  static const String webSocketProd = "wss://websocket.phoenix.zentropylabs.com/ws/prices";

  static const String webSocketUat = "wss://websocket.uat.phoenix.zentropylabs.com/ws/prices";
}
