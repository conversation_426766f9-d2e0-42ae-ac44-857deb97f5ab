import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:intl/intl.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/features/watchlist/bloc/watchist_bloc.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart';
import 'package:phoenix/screens/watchlist/add_security_screen.dart';
import 'package:phoenix/services/sort_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/toast/toast_utils.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';

import '../../features/common/position_comp_key.dart';

import '../../widgets/custom/dismissible/zen_dismissible.dart';
import '../../widgets/empty_state/empty_container.dart';
import '../../widgets/order_form/security_list_dropdown/security_list_text_formatter.dart';
import '../../widgets/search/custom_search_bar.dart';
import '../../widgets/search_refresh_sort_bar/search_refresh_sort_bar.dart';
import '../../widgets/tab_bar/dynamic_app_tab_bar.dart';
import '../../widgets/toast/custom_toast.dart';
import '../../features/watchlist/data/watchlist_provider.dart';
import 'widgets/rename_watchlist_dialog.dart';

class WatchlistScreen extends StatefulWidget {
  const WatchlistScreen({super.key});

  @override
  State<WatchlistScreen> createState() => _WatchlistScreenState();
}

class _WatchlistScreenState extends State<WatchlistScreen>
    with TickerProviderStateMixin {
  TabController? _tabController;
  final SortService _sortService = SortService.instance;
  bool _showSearch = false;
  final TextEditingController _searchController = TextEditingController();
  SortOption? _currentSortOption;
  bool _isAscending = true;

  Map<int, double> _stockPrices = {};
  final currencyFormat = NumberFormat.currency(locale: 'en_IN', symbol: '₹');
  Set<int> _subscribedZenIds = {};

  // Animation controller for floating action button
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize FAB animation controller
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Scale animation with bounce effect - scale in and out
    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    ));

    _loadWatchlistData();

    // Start the scale in animation after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _fabAnimationController.forward();
      }
    });
  }

  void _loadWatchlistData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is AuthAuthenticated) {
        // Clear any existing subscriptions
        _subscribedZenIds.clear();
        context
            .read<WatchistBloc>()
            .add(WatchlistData(authState.credentialsModel.clientId));
      }
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _searchController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _animateFAB() {
    _fabAnimationController.reset();
    _fabAnimationController.forward();
  }

  void _subscribeToWebsocket(List<Watchlist> watchlists) {
    final zenIds = watchlists
        .expand((watchlist) => watchlist.securities)
        .map((security) => security.zenId)
        .where((id) => id != 0)
        .toSet();

    // Only subscribe if the zen IDs have changed
    if (zenIds.isNotEmpty &&
            !zenIds.every((id) => _subscribedZenIds.contains(id)) ||
        zenIds.length != _subscribedZenIds.length) {
      _subscribedZenIds = zenIds;
      context
          .read<WebSocketBloc>()
          .add(WebSocketSelectMultipleStocks(zenIds.toList()));
    }
  }

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
      }
    });
  }

  void _showSortOptions() {
    _sortService.showSortOptions(
      context: context,
      currentSortOption: _currentSortOption,
      isAscending: _isAscending,
      onSortChanged: (option, ascending) {
        setState(() {
          _currentSortOption = option;
          _isAscending = ascending;
        });
      },
      availableOptions: [
        SortOption.alphabetical,
        SortOption.lastTradedPrice,
        SortOption.percentChange,
      ],
    );
  }

  List<SecurityModel> _sortSecurities(List<SecurityModel> securities) {
    if (_currentSortOption == null) return securities;

    final watchlistProvider = context.read<WatchlistProvider>();

    securities.sort((a, b) {
      int comparison = 0;

      switch (_currentSortOption!) {
        case SortOption.alphabetical:
          comparison = a.tradingSymbol.compareTo(b.tradingSymbol);
          break;
        case SortOption.lastTradedPrice:
          final priceA = _stockPrices[a.zenId] ?? 0.0;
          final priceB = _stockPrices[b.zenId] ?? 0.0;
          comparison = priceA.compareTo(priceB);
          break;
        case SortOption.percentChange:
          final priceA = _stockPrices[a.zenId] ?? 0.0;
          final priceB = _stockPrices[b.zenId] ?? 0.0;
          final sodPriceA = watchlistProvider.sodPrices[a.zenId] ?? 0.0;
          final sodPriceB = watchlistProvider.sodPrices[b.zenId] ?? 0.0;
          final changeA =
              sodPriceA != 0 ? ((priceA - sodPriceA) / sodPriceA) * 100 : 0.0;
          final changeB =
              sodPriceB != 0 ? ((priceB - sodPriceB) / sodPriceB) * 100 : 0.0;
          comparison = changeA.compareTo(changeB);
          break;
        default:
          comparison = a.tradingSymbol.compareTo(b.tradingSymbol);
      }

      return _isAscending ? comparison : -comparison;
    });
    return securities;
  }

  void _refreshWatchlist() {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      // Clear subscribed zen IDs to allow re-subscription
      _subscribedZenIds.clear();
      context
          .read<WatchistBloc>()
          .add(WatchlistData(authState.credentialsModel.clientId));
    }
  }

  void _addStock() {
    if (_tabController == null) return;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AddSecurityScreen(watchlistIndex: _tabController!.index),
      ),
    ).then((_) {
      _refreshWatchlist();
      // Animate the FAB again when user returns
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _animateFAB();
        }
      });
    });
  }

  void _showRenameDialog(List<Watchlist> watchlists) {
    if (_tabController == null) return;
    final currentWatchlist = watchlists[_tabController!.index];

    showDialog(
      context: context,
      builder: (context) => RenameWatchlistDialog(
        initialName: currentWatchlist.name,
        onRename: (newName) async {
          final authState = context.read<AuthBloc>().state;
          if (authState is AuthAuthenticated) {
            final watchlistProvider = context.read<WatchlistProvider>();
            await watchlistProvider.renameWatchlist(
              authState.credentialsModel.clientId.toString(),
              _tabController!.index,
              newName,
            );
            _refreshWatchlist();
          }
        },
      ),
    );
  }

  Widget _buildDeleteBackground() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeConstants.sellSlideOptionColor,
        borderRadius: const BorderRadius.horizontal(
          left: Radius.circular(20),
          right: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color.fromARGB(255, 255, 89, 89).withOpacity(0.9),
            offset: const Offset(-10, -10),
            blurRadius: 20,
            inset: true,
          ),
          const BoxShadow(
            color: Colors.black,
            offset: Offset(10, 10),
            blurRadius: 20,
            inset: true,
          ),
        ],
      ),
      child: const Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Icon(Icons.delete, color: Colors.white),
            SizedBox(width: 10),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      body: MultiBlocListener(
        listeners: [
          BlocListener<WebSocketBloc, WebSocketState>(
            listener: (context, state) {
              if (state is WebSocketMultipleStockPricesUpdated) {
                setState(() {
                  _stockPrices = state.stockPrices;
                });
              }
            },
          ),
          BlocListener<WatchistBloc, WatchistState>(
            listener: (context, state) {
              if (state is WatchlistLoaded) {
                final watchlists = state.watchlistData;
                // Subscribe to websocket for price updates only when watchlist data changes
                _subscribeToWebsocket(watchlists);
              }
            },
          ),
        ],
        child: BlocBuilder<WatchistBloc, WatchistState>(
          builder: (context, state) {
            if (state is PnlLoading) {
              return const Center(child: CircularLoader());
            } else if (state is WatchlistError) {
              return Center(child: Text('Error: ${state.error}'));
            } else if (state is WatchlistLoaded) {
              final watchlists = state.watchlistData;

              if (watchlists.isEmpty) {
                return const Center(child: Text('No watchlists found.'));
              }

              // Initialize TabController when watchlists are loaded
              if (_tabController == null ||
                  _tabController!.length != watchlists.length) {
                final oldIndex = _tabController?.index ?? 0;
                _tabController?.dispose();
                _tabController = TabController(
                  length: watchlists.length,
                  vsync: this,
                  initialIndex: oldIndex < watchlists.length ? oldIndex : 0,
                );
              }

              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: DynamicAppTabBar(
                          controller: _tabController!,
                          tabs: watchlists
                              .map((w) => TabData(
                                  title: w.name, count: w.securities.length))
                              .toList(),
                          showCount: false,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit,
                            color: ThemeConstants.zenWhite, size: 22),
                        onPressed: () => _showRenameDialog(watchlists),
                      ),
                    ],
                  ),
                  SearchRefreshSortBar(
                    refresh: _refreshWatchlist,
                    toggleSearch: _toggleSearch,
                    showSortOptions: _showSortOptions,
                    isAscending: _isAscending,
                    currentSortOption: _currentSortOption,
                  ),
                  if (_showSearch)
                    CustomSearchBar(
                      controller: _searchController,
                      hintText: 'Search watchlist...',
                      autofocus: true,
                      onSearch: (query) {
                        setState(() {});
                      },
                      onClose: _toggleSearch,
                    ),
                  Expanded(
                    child: BlocBuilder<PortfolioBloc, PortfolioState>(
                      builder: (context, portfolioState) {
                        List<PositionsModel> positions = [];
                        if (portfolioState is PortfolioLoaded) {
                          positions = portfolioState.openPositions;
                        }

                        return TabBarView(
                          controller: _tabController!,
                          children: watchlists.asMap().entries.map((entry) {
                            final index = entry.key;
                            final watchlist = entry.value;
                            return _buildWatchlistTab(
                                watchlist, positions, index);
                          }).toList(),
                        );
                      },
                    ),
                  ),
                ],
              );
            }

            return const Center(child: CircularLoader());
          },
        ),
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 80.0, right: 4),
        child: AnimatedBuilder(
          animation: _fabAnimationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _fabScaleAnimation.value,
              child: FloatingActionButton(
                onPressed: _addStock,
                backgroundColor: ThemeConstants.floatingActionButtonColor,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(Icons.add, size: 28, color: Colors.white),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildWatchlistTab(
    Watchlist watchlist,
    List<PositionsModel> positions,
    int watchlistIndex,
  ) {
    final watchlistProvider = context.read<WatchlistProvider>();
    final sodPrices = watchlistProvider.sodPrices;

    List<SecurityModel> filteredSecurities = _searchController.text.isEmpty
        ? watchlist.securities
        : watchlist.securities
            .where((security) => security.tradingSymbol
                .toLowerCase()
                .contains(_searchController.text.toLowerCase()))
            .toList();

    // Apply sorting if user has explicitly chosen a sort option
    if (_currentSortOption != null) {
      filteredSecurities = _sortSecurities(filteredSecurities);
    }

    if (filteredSecurities.isEmpty) {
      return const EmptyContainer(
        title: "Watchlist is Empty",
        message: "Add securities to this watchlist to see them here.",
        imagePath: "images/database-positions-no.png",
      );
    }

    return RefreshIndicator(
      onRefresh: () async => _refreshWatchlist(),
      child: _searchController.text.isEmpty
          ? Theme(
              data: Theme.of(context).copyWith(
                canvasColor: ThemeConstants.blue.withOpacity(0.5),
              ),
              child: ReorderableListView.builder(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                itemCount: filteredSecurities.length,
                onReorder: (oldIndex, newIndex) async {
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }

                  final authState = context.read<AuthBloc>().state;
                  if (authState is AuthAuthenticated) {
                    // Update local state immediately for instant UI feedback
                    setState(() {
                      _currentSortOption = null;
                      final securities = watchlist.securities;
                      final security = securities.removeAt(oldIndex);
                      securities.insert(newIndex, security);
                    });

                    // Persist changes in background
                    try {
                      final watchlistProvider =
                          context.read<WatchlistProvider>();
                      await watchlistProvider.updateWatchlistOrder(
                        authState.credentialsModel.clientId.toString(),
                        watchlistIndex,
                        watchlist.securities,
                      );
                    } catch (e) {
                      // If save fails, refresh to restore correct state
                      _refreshWatchlist();
                      ToastUtil.showToast(
                        context,
                        'Failed to save order',
                        ToastType.error,
                      );
                    }
                  }
                },
                itemBuilder: (context, index) {
                  final security = filteredSecurities[index];
                  return _buildSecurityListTile(
                    security,
                    index,
                    positions,
                    sodPrices,
                    false,
                  );
                },
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              itemCount: filteredSecurities.length,
              itemBuilder: (context, index) {
                return _buildSecurityListTile(
                  filteredSecurities[index],
                  index,
                  positions,
                  sodPrices,
                  true,
                );
              },
            ),
    );
  }

  Widget _buildSecurityListTile(
      SecurityModel security,
      int index,
      List<PositionsModel> positions,
      Map<int, double> sodPrices,
      bool isSearchMode) {
    final price = _stockPrices[security.zenId];
    final sodPrice = sodPrices[security.zenId] ?? 0.0;
    final change = price != null && sodPrice != 0 ? price - sodPrice : 0.0;
    final percentageChange = price != null && sodPrice != 0
        ? ((price - sodPrice) / sodPrice) * 100
        : 0.0;
    final isGain = change >= 0;

    final position = positions.firstWhere(
      (p) => p.tradingSymbol == security.tradingSymbol,
      orElse: () => PositionsModel(
        positionCompositeKey: PositionCompKey(
          zenSecId: 0,
          clientId: 0,
          broker: '',
          accountId: 0,
          strategyId: 0,
        ),
        tradingSymbol: '',
        position: 0,
        openCost: 0,
        averageCostPerShare: 0,
        latestPrice: 0,
        sodPrice: sodPrice,
        unrealizedPnl: 0,
        unrealizedPnlPercentageChange: 0,
        realizedPnl: 0,
        curMarketValue: 0,
        date: DateTime.now(),
        isOpen: false,
      ),
    );
    final qty = position.position;

    final listTile = Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: ThemeConstants.backgroundColor,
        boxShadow: ThemeConstants.neomorpicShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left Column
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  SecurityListTextFormatter.format(
                      security, security.instrumentType),
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  security.exchanges.first,
                  style: const TextStyle(
                    color: Color(0xffCDCDCD),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 18),
                Text(
                  security.name,
                  style: const TextStyle(
                    color: Color(0xffCDCDCD),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                //const SizedBox(height: 8),
                // LTP and Change Percentage
                // RichText(
                //   text: TextSpan(
                //     children: [
                //       const TextSpan(
                //         text: 'LTP ',
                //         style: TextStyle(
                //           color: Colors.white,
                //           fontSize: 12,
                //           fontWeight: FontWeight.w400,
                //         ),
                //       ),
                //       TextSpan(
                //         text: price != null ? UtilFunctions.formatIndianCurrency(price) : "---",
                //         style: const TextStyle(
                //           color: Color(0xffCDCDCD),
                //           fontSize: 12,
                //           fontWeight: FontWeight.w400,
                //         ),
                //       ),
                //       _daysPnlFormatter(change, percentageChange),
                //     ],
                //   ),
                // ),
              ],
            ),
          ),

          // Right Column
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (qty != 0)
                Row(
                  children: [
                    const ImageIcon(
                      AssetImage("images/tile-generic/qty_icon.png"),
                      color: Colors.blue,
                      size: 13,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      qty.toString(),
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 24),
              Text(
                price != null
                    ? "₹${UtilFunctions.formatIndianCurrency(price)}"
                    : "---",
                style: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 15,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: isGain
                      ? ThemeConstants.tileGreenColor.withOpacity(0.1)
                      : ThemeConstants.titleRedColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '${change.toStringAsFixed(2)} (${percentageChange.toStringAsFixed(2)}%)',
                  style: TextStyle(
                    color: isGain
                        ? ThemeConstants.tileGreenColor
                        : ThemeConstants.titleRedColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );

    if (isSearchMode) {
      return Padding(
        key: Key('search_${security.tradingSymbol}_$index'),
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
        child: listTile,
      );
    }

    return Padding(
      key: Key('reorder_${security.tradingSymbol}_$index'),
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
      child: ZenDismissible(
        key: Key('dismissible_${security.tradingSymbol}_$index'),
        secondaryBackground: _buildDeleteBackground(),
        background: _buildDeleteBackground(),
        direction: DismissDirection.endToStart,
        onDismissed: (direction) async {
          final authState = context.read<AuthBloc>().state;
          if (authState is AuthAuthenticated) {
            try {
              final watchlistProvider = context.read<WatchlistProvider>();
              final success = await watchlistProvider.deleteWatchlists(
                  authState.credentialsModel.clientId.toString(),
                  _tabController!.index,
                  security.tradingSymbol);

              if (success) {
                _refreshWatchlist();
                ZenSnackbar.show(
                  context,
                  '${security.tradingSymbol} removed from watchlist.',
                  type: ToastType.info,
                );
              } else {
                ZenSnackbar.show(
                  context,
                  'Failed to remove ${security.tradingSymbol}.',
                  type: ToastType.error,
                );
              }
            } catch (e) {
              ZenSnackbar.show(
                context,
                'Error removing security.',
                type: ToastType.error,
              );
            }
          }
        },
        child: listTile,
      ),
    );
  }
}
