import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_data_map/bloc/broker_data_map_bloc.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/empty_state/empty_container.dart';
import 'package:phoenix/widgets/tile/tile_generic.dart';

class OrdersListBuilder extends StatelessWidget {
  final List<dynamic> data;
  final String emptyMessage;
  final AnimationController formSheetAnimeController;
  bool isOrderOpen;

  OrdersListBuilder({
    super.key,
    required this.data,
    required this.emptyMessage,
    required this.formSheetAnimeController,
    this.isOrderOpen = false,
  });

  @override
  Widget build(BuildContext context) {
    Future<void> pullRefresh() async {
      final authState = context.read<AuthBloc>().state; // Get current state

      if (authState is AuthAuthenticated) {
        context.read<OrdersStateBloc>().add(
              FetchOrdersState(
                clientId: authState.credentialsModel.clientId,
              ),
            );
      } else {
        // Handle unauthenticated case (e.g., show login dialog)
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("You need to log in first")),
        );
      }
    }

    if (data.isEmpty) {
      return EmptyContainer(
          title: isOrderOpen ? "No Open Orders" : "No Orders Placed",
          message: "You can place an order from the watchlist.",
          imagePath: "images/no_orders.png");
    }

    return BlocBuilder<WebSocketBloc, WebSocketState>(
      builder: (context, state) {
        Map<int, double> stockPrices = {};

        if (state is WebSocketMultipleStockPricesUpdated) {
          stockPrices = state.stockPrices;
        }

         final brokerDataMapState = context.read<BrokerDataMapBloc>().state;
        if (brokerDataMapState is! BrokerDataMapProcessedState) {
          return Center(child: Text("Something went wrong..."));
        }

        return RefreshIndicator(
          color: ThemeConstants.blue,
          backgroundColor: ThemeConstants.backgroundColor,
          onRefresh: pullRefresh,
          child: ListView.builder(
            clipBehavior: Clip.hardEdge,
            padding: const EdgeInsets.all(6.0),
            itemCount: data.length + 1,
            itemBuilder: (context, index) {
              if (index == data.length) {
                // Add a SizedBox at the end
                return const SizedBox(height: 50); // Adjust height as needed
              }
              final item = data[index] as UnifiedOrderData;
              final int zenId = item.positionCompKey.zenSecId;
              final double? priceUpdate = stockPrices[zenId];

              // Broker Account Strategy id Map
              final brokerName = brokerDataMapState
                      .brokerNameToLabelMap[item.positionCompKey.broker] ??
                  "N/A";
              final accountName = brokerDataMapState.accountIdToNameMap[
                      item.positionCompKey.accountId] ??
                  "N/A";
              final strategyName = brokerDataMapState.strategyIdToNameMap[
                      item.positionCompKey.strategyId] ??
                  "N/A";
              final brokerMetaData = BrokerAccountStrategyData(
                brokerName: brokerName,
                accountId: item.positionCompKey.accountId,
                strategyId: item.positionCompKey.strategyId,
                accountName: accountName,
                strategyName: strategyName,
              );


              return TileGeneric(
                data: item,
                tileType: "zenOrder",
                formAnimeController: formSheetAnimeController,
                isOrderOpen: isOrderOpen,
                prices: priceUpdate,
                brokerAccountStrategyMapData: brokerMetaData,
              );
            },
          ),
        );
      },
    );
  }
}
