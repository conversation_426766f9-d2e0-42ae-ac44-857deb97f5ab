import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/features/pnl_graph/bloc/pnl_graph_bloc.dart';
import 'package:phoenix/features/pnl_graph/repository/pnl_graph_formatter.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/graphs/double_line_graph.dart';
import 'package:phoenix/widgets/graphs/line_graph.dart';

enum dataRange {
  oneMonth,
  threeMonth,
  sixMonth,
  oneYear,
  max,
}

class PnlGraphContainer extends StatefulWidget {
  final PositionPnL data;
  final bool isRealized;
  final bool isUnrealized;

  const PnlGraphContainer({super.key, required this.data, required this.isRealized, required this.isUnrealized});

  @override
  _PnlGraphContainerState createState() => _PnlGraphContainerState();
}

class _PnlGraphContainerState extends State<PnlGraphContainer> {
  dataRange selectedRange = dataRange.oneMonth;
  @override
  void initState() {
    super.initState();
    debugPrint("PNL Dashboard init");
    _fetchPnlGraphData();
  }

  void _fetchPnlGraphData({int days = 30}) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      final DateTime now = DateTime.now();
      final DateTime oneMonthAgo = now.subtract(Duration(days: days));

      final String startTimestamp =
          '${oneMonthAgo.year.toString().padLeft(4, '0')}-${oneMonthAgo.month.toString().padLeft(2, '0')}-${oneMonthAgo.day.toString().padLeft(2, '0')} 00:00:00';
      final String endTimestamp =
          '${now.year.toString().padLeft(4, '0')}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} 00:00:00';
      context.read<PnlGraphBloc>().add(
            FetchPnlGraphData(
              clientId: authState.credentialsModel.clientId,
              startTimestamp: startTimestamp,
              endTimestamp: endTimestamp,
              zenSecIds: [widget.data.positionCompositeKey.zenSecId],
              requestId: DateTime.now().millisecondsSinceEpoch.toString(), //to track the request correctly
              strategyId: [widget.data.positionCompositeKey.strategyId],
            ),
          );
    }
  }

  Widget _buildTimeButton(String text, dataRange range) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (!(selectedRange == range)) {
          setState(() {
            selectedRange = range;
          });
        }
        switch (range) {
          case dataRange.oneMonth:
            _fetchPnlGraphData(days: 30);
            break;
          case dataRange.threeMonth:
            _fetchPnlGraphData(days: 90);
            break;
          case dataRange.sixMonth:
            _fetchPnlGraphData(days: 180);
            break;
          case dataRange.oneYear:
            _fetchPnlGraphData(days: 365);
            break;
          case dataRange.max:
            _fetchPnlGraphData(days: 365 * 10);
            break;
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        width: 45,
        alignment: Alignment.center,
        height: 25,
        decoration: BoxDecoration(
          color: selectedRange == range ? Colors.blue : ThemeConstants.zenBlack,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight:
                selectedRange == range ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        BlocConsumer<PnlGraphBloc, PnlGraphState>(
          listener: (context, state) {
            if (state is PnlGraphError) {
              debugPrint(state.error);
            }
            if (state is PnlGraphLoaded) {
              debugPrint("PNL Graph Loaded");
              context.read<PnlGraphBloc>().add(
                    FormatPnlGraphData(
                      type: PnLPeriodType.ltd,
                      isRealized:  widget.isRealized,
                      isUnrealized: widget.isUnrealized,
                    ),
                  );
            }
            if (state is PnlGraphLoading) {
              debugPrint("PNL Graph Loading");
            }
          },
          builder: (context, state) {
            if (state is PnlGraphLoading) {
              return SizedBox(
                height: 180, // Set an appropriate height
                child: const Center(
                  child: SizedBox(
                    width: 25,
                    height: 25,
                    child: CircularProgressIndicator(
                      backgroundColor: Color(0xff338AFF),
                      strokeCap: StrokeCap.square,
                      color: ThemeConstants.zenGrey,
                      strokeWidth: 3,
                      
                      
                    ),
                  ),
                ),
              );
            } else if (state is PnlGraphFormatted) {
              //this date here is used to conver the x value to a date

              final bool isRealized = state.isRealized;

              final Color lineColor = isRealized
                  ? ThemeConstants.netWorthHeaderColor
                  : const Color(0xFF4A90E2);

              debugPrint("PNL Graph Formatted");
              return SizedBox(
                  height: 180, // Set an appropriate height
                  child: LineGraph(
                    chartPoints: state.chartPoints,
                    baseDate: state.baseDate,
                    lineColor: lineColor,
                  ));
            } else if (state is PnlGraphBothFormatted) {
              final DateTime baseDate = state.baseDate;
              debugPrint("PNL Graph Both Formatted");
              return SizedBox(
                height: 180, // Set an appropriate height
                child: DoubleLineGraph(
                  baseDate: baseDate,
                  lineOnePoints: state.realizedPoints,
                  lineTwoPoints: state.unrealizedPoints,
                  lineOneColor: ThemeConstants.netWorthHeaderColor,
                  lineTwoColor: const Color(0xFF4A90E2),
                ),
              );
            }

            return SizedBox(
              height: 200, // Set an appropriate height
              child: GestureDetector(
                onTap: () {
                  _fetchPnlGraphData();
                },
                child: Center(
                  child: ThemeConstants.zenText(
                    "Something went wrong, please try again later.",
                  ),
                ),
              ),
            );
          },
        ),
        //Time Period Buttons (placeholder for chart area)
        Container(
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTimeButton('1M', dataRange.oneMonth),
              _buildTimeButton('3M', dataRange.threeMonth),
              _buildTimeButton('6M', dataRange.sixMonth),
              _buildTimeButton('1Y', dataRange.oneYear),
              _buildTimeButton('MAX', dataRange.max),
            ],
          ),
        ),
      ],
    );
  }
}
