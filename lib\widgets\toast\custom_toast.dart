import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

enum ToastType {
  success,
  error,
  warning,
  info,
}

class CustomToast extends StatelessWidget {
  final String message;
  final ToastType type;
  final VoidCallback onClose;

  const CustomToast({
    super.key,
    required this.message,
    required this.type,
    required this.onClose,
  });

  Color _getBackgroundColor() {
    switch (type) {
      case ToastType.success:
        return ThemeConstants
            .toastSuccessColor; // Dark green color from the image
      case ToastType.error:
        return ThemeConstants.toastFailedColor;
      case ToastType.warning:
        return ThemeConstants.toastFillMandatoryFeildsColor;
      case ToastType.info:
        return Colors.blue.shade900;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            message,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: onClose,
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }
}

class ZenSnackbar {
  static void show(BuildContext context, String message, {ToastType type = ToastType.info, Duration duration =  const Duration(seconds: 2)}) {

    final backgroundColor = _getBackgroundColor(type);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message,style: TextStyle(fontWeight: FontWeight.w600),),
        backgroundColor: backgroundColor,
        duration: duration,
        dismissDirection: DismissDirection.down,
        showCloseIcon: true,
      ),
    );
  }

  static Color _getBackgroundColor(ToastType type) {
    switch (type) {
      case ToastType.success:
        return Colors.green;
      case ToastType.error:
        return Colors.red;
      case ToastType.warning:
        return Colors.orangeAccent;
      case ToastType.info:
        return Colors.blue.shade900;
    }
  }
}
