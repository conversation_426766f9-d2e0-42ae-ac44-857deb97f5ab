import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/services/shared_prefrences_service.dart';
import 'package:phoenix/widgets/broker_account_strategy_selector/broker_account_strategy_modal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';

import '../../features/bottom_navigation/bloc/bottom_navigation_bloc.dart';
import '../../features/orders_state/bloc/orders_state_bloc.dart';
import '../../features/pnl/bloc/pnl_bloc.dart';
import '../../features/portfolio_data/bloc/portfolio_bloc.dart';

class ClientSelectorModal extends StatefulWidget {
  final List clientsList;
  final int currentClientIndex;
  final ValueChanged<int> onSelected;

  const ClientSelectorModal({
    super.key,
    required this.clientsList,
    required this.currentClientIndex,
    required this.onSelected,
  });

  @override
  State<ClientSelectorModal> createState() => _ClientSelectorModalState();
}

class _ClientSelectorModalState extends State<ClientSelectorModal> {
  late Future<Map<String, dynamic>> _settingsFuture;

  @override
  void initState() {
    super.initState();
    _settingsFuture = _fetchSettings();
  }

  Future<Map<String, dynamic>> _fetchSettings() async {
    final prefs = SharedPreferencesService.instance;
    final authState = BlocProvider.of<AuthBloc>(context).state;
    int? clientId;
    if (authState is AuthAuthenticated) {
      clientId = authState.credentialsModel.clientId;
    }
    return {
      'broker': clientId != null ? prefs.getDefaultBroker(clientId) : null,
      'account': clientId != null
          ? (prefs.getDefaultAccountName(clientId) ?? '-')
          : '-',
      'strategy': clientId != null
          ? (prefs.getDefaultStrategyName(clientId) ?? '-')
          : '-',
    };
  }

  Future<void> _showAndHandleSettingsDialog(BuildContext context) async {
    final authBloc = BlocProvider.of<AuthBloc>(context);
    final authState = authBloc.state;
    final credentials = (authState is AuthAuthenticated)
        ? authState.credentialsModel
        : null;
    if (credentials == null) return;
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => BlocProvider(
        create: (_) => BrokerAccountStrategySelectionBloc(credentials),
        child: BrokerAccountStrategyModal(
          onDefaultSet: (clientId) async {
            int currentIndex =
                (BlocProvider.of<BottomNavigationBloc>(context).state
                        is BottomNavigaionChanged)
                    ? (BlocProvider.of<BottomNavigationBloc>(context)
                            .state as BottomNavigaionChanged)
                        .currentPageIndex
                    : 0;

            // Trigger refresh based on current page
            switch (currentIndex) {
              case 0: // Portfolio Screen
                context.read<PortfolioBloc>().add(
                      FetchPortfolio(
                        clientId,
                      ),
                    );
                break;
              case 1: // PnL Screen
                context.read<PnlBloc>().add(
                      FetchPnlData(
                        clientId,
                      ),
                    );
                break;
              case 2: // Orders Screen
                context.read<OrdersStateBloc>().add(
                      FetchOrdersState(clientId: clientId),
                    );
                break;
              case 3: // Spider Screen
                // Do Nothing for Spider Screen
                break;
            }
          },
        ),
      ),
    );
    if (result != null &&
        result['brokerName'] != null &&
        result['accountId'] != null &&
        result['strategyId'] != null) {
      final prefs = SharedPreferencesService.instance;
      final authState = BlocProvider.of<AuthBloc>(context).state;
      int? clientId;
      if (authState is AuthAuthenticated) {
        clientId = authState.credentialsModel.clientId;
      }
      if (clientId != null) {
        prefs.saveDefaultSelections(
          clientId: clientId,
          brokerName: result['brokerName'],
          accountId: result['accountId'].toString(),
          strategyId: result['strategyId'].toString(),
          accountName: result['accountName'],
          strategyName: result['strategyName'],
        );
        setState(() {
          _settingsFuture = _fetchSettings();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar at the top
          Center(
            child: Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade600,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          // Header with title
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Select Client',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white70),
                onPressed: () => Navigator.of(context).pop(),
                splashRadius: 20,
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Client List
          Flexible(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade900.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: ListView.separated(
                  shrinkWrap: true,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: widget.clientsList.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 1,
                    color: Colors.grey.shade800.withOpacity(0.5),
                    indent: 16,
                    endIndent: 16,
                  ),
                  itemBuilder: (context, i) {
                    final isSelected = i == widget.currentClientIndex;
                    return Column(
                      children: [
                        ListTile(
                          onTap: () {
                            Navigator.pop(context, i);
                          },
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 4),
                          leading: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? ThemeConstants.blue.withOpacity(0.15)
                                  : Colors.grey.shade800.withOpacity(0.3),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: isSelected
                                  ? Icon(Icons.check_circle,
                                      color: ThemeConstants.blue, size: 20)
                                  : Icon(Icons.circle_outlined,
                                      color: Colors.grey[500], size: 20),
                            ),
                          ),
                          title: Text(
                            widget.clientsList[i].clientName,
                            style: TextStyle(
                              color: isSelected
                                  ? Colors.white
                                  : ThemeConstants.zenWhite,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.w500,
                              fontSize: 15,
                            ),
                          ),
                          tileColor: isSelected
                              ? ThemeConstants.blue.withOpacity(0.08)
                              : null,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        if (isSelected) _buildSelectedClientDetails(context),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSelectedClientDetails(BuildContext context) {
    return _CollapsibleSettingsSection(
      builder: (context, isExpanded) {
        return FutureBuilder(
          future: _settingsFuture,
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return SizedBox(
                height: 80,
                child: Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(ThemeConstants.blue),
                    ),
                  ),
                ),
              );
            }
            final data = snapshot.data as Map<String, dynamic>;
            if (!isExpanded) {
              return _buildCollapsedView(context, data);
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow(
                  icon: Icons.account_balance_wallet,
                  label: 'Broker',
                  value: data['broker'] ?? '-',
                ),
                const SizedBox(height: 12),
                _buildDetailRow(
                  icon: Icons.account_box,
                  label: 'Account',
                  value: data['account'] ?? '-',
                ),
                const SizedBox(height: 12),
                _buildDetailRow(
                  icon: Icons.auto_graph,
                  label: 'Strategy',
                  value: data['strategy'] ?? '-',
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeConstants.blue,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(0, 42),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                      elevation: 0,
                    ),
                    icon: const Icon(Icons.edit, size: 18),
                    label: const Text(
                      'Modify Settings',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onPressed: () => _showAndHandleSettingsDialog(context),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildCollapsedView(BuildContext context, Map<String, dynamic> data) {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: ThemeConstants.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child:
                    Icon(Icons.settings, size: 18, color: ThemeConstants.blue),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Settings',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade400,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${data['broker'] ?? '-'} • ${data['account'] ?? '-'}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeConstants.blue,
            foregroundColor: Colors.white,
            minimumSize: const Size(0, 36),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            elevation: 0,
          ),
          child: const Text(
            'Edit',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
          onPressed: () => _showAndHandleSettingsDialog(context),
        ),
      ],
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: ThemeConstants.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 18, color: ThemeConstants.blue),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade400,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _CollapsibleSettingsSection extends StatefulWidget {
  final Widget Function(BuildContext context, bool isExpanded) builder;

  const _CollapsibleSettingsSection({
    required this.builder,
  });

  @override
  State<_CollapsibleSettingsSection> createState() =>
      _CollapsibleSettingsSectionState();
}

class _CollapsibleSettingsSectionState
    extends State<_CollapsibleSettingsSection> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade900.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeConstants.blue.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Animated content
          AnimatedCrossFade(
            firstChild: Padding(
              padding: const EdgeInsets.all(16),
              child: widget.builder(context, false),
            ),
            secondChild: Padding(
              padding: const EdgeInsets.all(16),
              child: widget.builder(context, true),
            ),
            crossFadeState: _isExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
            firstCurve: Curves.easeInOut,
            secondCurve: Curves.easeInOut,
            sizeCurve: Curves.easeInOut,
          ),
          // Expand/Collapse button
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade800.withOpacity(0.3),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _isExpanded ? 'Show Less' : 'Show More',
                      style: TextStyle(
                        color: Colors.grey.shade300,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      _isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      size: 16,
                      color: Colors.grey.shade300,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
