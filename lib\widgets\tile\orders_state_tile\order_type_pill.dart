import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class OrderType<PERSON>ill extends StatelessWidget {
  final String orderType;

  const OrderTypePill({super.key, required this.orderType});

  String _getOrderTypeText(String orderType) {
    switch (orderType) {
      case 'MARKET':
        return 'M';
      case 'LIMIT':
        return 'L';
      case 'STANDALONE_SL_LIMIT':
        return 'SL-L';
      case 'STANDALONE_SL_MARKET':
        return 'SL-M';
      case 'LIMIT_ORDER_WITH_SL_LIMIT':
        return 'L SL-L';
      case 'MARKET_ORDER_WITH_SL_LIMIT':
        return 'M SL-L';
      case 'LIMIT_ORDER_WITH_SL_MARKET':
        return 'L SL-M';
      case 'MARKET_ORDER_WITH_SL_MARKET':
        return 'M SL-M';
      case 'TRAILING_STOP_LOSS_MARKET_ORDER':
        return 'T SL-M';
      case 'TRAILING_STOP_LOSS_LIMIT_ORDER':
        return 'T SL-L';
      default:
        return orderType;
    }
  }


  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: ThemeConstants.blue.withValues(alpha: 0.5),
          width: 1,
        ),
        color: ThemeConstants.blue.withValues(alpha: 0.2),
      ),
      child: Text(
        _getOrderTypeText(orderType),
        textAlign: TextAlign.center,
        style: const TextStyle(
          color: ThemeConstants.zenWhite,
          fontSize: 11,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
