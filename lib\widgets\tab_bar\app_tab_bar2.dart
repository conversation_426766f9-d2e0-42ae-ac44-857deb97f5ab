import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class AppTabBar2 extends StatelessWidget {
  final int count1;
  final int count2;
  final int? count3;
  final String title1;
  final String title2;
  final String? title3;
  final TabController? controller;

  const AppTabBar2({
    super.key,
    required this.count1,
    required this.count2,
    required this.title1,
    required this.title2,
    this.count3,
    this.title3,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final tabs = <Widget>[
      Container(
        height: 32,
        child: Tab(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                fit: FlexFit.loose,
                child: Text(
                  title1,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: ThemeConstants.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  "$count1",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeConstants.blue,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      Container(
        height: 32,
        child: Tab(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                fit: FlexFit.loose,
                child: Text(
                  title2,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: ThemeConstants.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  "$count2",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                   color: ThemeConstants.blue,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ];
    if (title3 != null && count3 != null) {
      tabs.add(
        Container(
          height: 32,
          child: Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  fit: FlexFit.loose,
                  child: Text(
                    title3!,
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: ThemeConstants.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    "$count3",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: ThemeConstants.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    return Container(
      width: MediaQuery.of(context).size.width * 0.90,
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      alignment: Alignment.center,
      child: TabBar(
        controller: controller,
        isScrollable: false,
        indicatorColor: ThemeConstants.blue,
        indicatorPadding: EdgeInsets.symmetric(vertical: -1),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(width: 1.0, color: ThemeConstants.blue),
          insets: EdgeInsets.only(
            right: 30.0,
            left: 20.0,
          ),
        ),
        labelColor: ThemeConstants.blue,
        unselectedLabelColor: Colors.white,
        dividerColor: Colors.transparent,
        labelPadding: EdgeInsets.zero,
        tabs: tabs,
      ),
    );
  }
}
