import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/utils/theme_constants.dart';

class CustomSearchBar extends StatelessWidget {
  final ValueChanged<String> onSearch;
  final String hintText;
  final TextEditingController? controller;
  final bool autofocus;
  final VoidCallback? onClose;

  const CustomSearchBar({
    super.key,
    required this.onSearch,
    this.hintText = 'Search...',
    this.controller,
    this.autofocus = false,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xff353535),
        borderRadius: BorderRadius.circular(10),
        // boxShadow: ThemeConstants.neomorpicShadow,
      ),
      child: TextField(
        controller: controller,
        autofocus: autofocus,
        textAlignVertical: TextAlignVertical.center,
        cursorColor: ThemeConstants.blue,
        style: const TextStyle(
          color: Color(0xffADADAD),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(
            color: Color(0xffADADAD),

            fontSize: 16,
          ),
          prefixIcon: const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Icon(
              Icons.search,
              color: Color(0xffADADAD),
              size: 20,
            ),
          ),
          suffixIcon: onClose != null ? Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: IconButton(
              icon: const Icon(
                Icons.close,
                color: Color(0xffADADAD),
                size: 20,
              ),
              onPressed: onClose,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ) : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 10,
          ),
          /// this is used to aling the text and icon in same line
          isDense: false,
        ),
        onChanged: onSearch,
      ),
    );
  }
}


