import 'package:flutter/material.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart';
import 'package:phoenix/services/sort_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/search/custom_search_bar.dart';
import 'package:phoenix/widgets/search_refresh_sort_bar/search_refresh_sort_bar.dart';
import 'package:phoenix/widgets/tab_bar/dynamic_app_tab_bar.dart';

class WatchlistHeader extends StatelessWidget {
  final TabController tabController;
  final List<Watchlist> watchlists;
  final bool showSearch;
  final TextEditingController searchController;
  final SortOption? currentSortOption;
  final bool isAscending;
  final VoidCallback onRefresh;
  final VoidCallback onToggleSearch;
  final VoidCallback onShowSortOptions;
  final VoidCallback onShowRenameDialog;
  final VoidCallback onSearchChanged;

  const WatchlistHeader({
    super.key,
    required this.tabController,
    required this.watchlists,
    required this.showSearch,
    required this.searchController,
    required this.currentSortOption,
    required this.isAscending,
    required this.onRefresh,
    required this.onToggleSearch,
    required this.onShowSortOptions,
    required this.onShowRenameDialog,
    required this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab Bar with Edit Button
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: DynamicAppTabBar(
                controller: tabController,
                tabs: watchlists
                    .map((w) => TabData(
                        title: w.name, count: w.securities.length))
                    .toList(),
                showCount: false,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: ThemeConstants.zenWhite),
              onPressed: onShowRenameDialog,
            ),
          ],
        ),
        
        // Search, Refresh, Sort Bar
        SearchRefreshSortBar(
          refresh: onRefresh,
          toggleSearch: onToggleSearch,
          showSortOptions: onShowSortOptions,
          isAscending: isAscending,
          currentSortOption: currentSortOption,
        ),
        
        // Search Bar (conditionally shown)
        if (showSearch)
          CustomSearchBar(
            controller: searchController,
            hintText: 'Search watchlist...',
            autofocus: true,
            onSearch: (query) => onSearchChanged(),
            onClose: onToggleSearch,
          ),
      ],
    );
  }
}