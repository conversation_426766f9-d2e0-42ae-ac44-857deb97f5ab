import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/theme_constants.dart';

class ChatMessageBubble extends StatelessWidget {
  final String text;
  final bool isUser;
  const ChatMessageBubble({super.key, required this.text, required this.isUser});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: GestureDetector(
        onLongPress: () async {
          final overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
          final box = context.findRenderObject() as RenderBox;
          final offset = box.localToGlobal(Offset.zero, ancestor: overlay);
          final result = await showMenu(
            context: context,
            position: RelativeRect.fromLTRB(
              offset.dx + box.size.width / 2,
              offset.dy,
              offset.dx + box.size.width / 2,
              offset.dy + box.size.height,
            ),
            items: [
              PopupMenuItem<int>(
                value: 0,
                child: Row(
                  children: [
                    Icon(Icons.copy, color: ThemeConstants.blue, size: 20),
                    SizedBox(width: 8),
                    Text('Copy', style: TextStyle(fontWeight: FontWeight.w500, color: Colors.white)),
                  ],
                ),
              ),
            ],
            color: ThemeConstants.zenBlack1,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          );
          if (result == 0) {
            Clipboard.setData(ClipboardData(text: text));
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.check, color: ThemeConstants.blue, size: 20),
                    SizedBox(width: 8),
                    Text('Message copied'),
                  ],
                ),
                backgroundColor: ThemeConstants.zenBlack1,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                margin: EdgeInsets.only(
                  bottom: 80,
                  left: 24,
                  right: 24,
                ),
                duration: Duration(seconds: 1),
              ),
            );
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 18),
          decoration: BoxDecoration(
            gradient: isUser
                ? LinearGradient(colors: [
                    ThemeConstants.blue,
                    ThemeConstants.blue.withOpacity(0.7)
                  ])
                : LinearGradient(colors: [
                    ThemeConstants.zenBlack,
                    ThemeConstants.zenBlack1
                  ]),
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(20),
              topRight: const Radius.circular(20),
              bottomLeft: Radius.circular(isUser ? 20 : 4),
              bottomRight: Radius.circular(isUser ? 4 : 20),
            ),
            boxShadow: const [
              BoxShadow(color: Colors.black26, blurRadius: 8, offset: Offset(0, 2)),
            ],
          ),
          child: Text(
            text,
            style: TextStyle(
              color: isUser ? Colors.white : ThemeConstants.zenWhite,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
