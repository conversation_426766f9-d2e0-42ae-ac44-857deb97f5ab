import 'package:flutter/material.dart';
import 'package:phoenix/widgets/animated_typing_dots.dart';
import '../../utils/theme_constants.dart';

class TypingIndicator extends StatelessWidget {
  const TypingIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 20),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 18),
        decoration: BoxDecoration(
          color: ThemeConstants.zenBlack,
          borderRadius: BorderRadius.circular(20),
          boxShadow: const [
            BoxShadow(color: Colors.black26, blurRadius: 8, offset: Offset(0, 2)),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 24,
              child: DefaultTextStyle(
                style: TextStyle(
                  color: ThemeConstants.zenWhite,
                  fontSize: 18,
                ),
                child: AnimatedTypingDots(),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'AI is typing...',
              style: TextStyle(
                color: ThemeConstants.zenGrey,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
