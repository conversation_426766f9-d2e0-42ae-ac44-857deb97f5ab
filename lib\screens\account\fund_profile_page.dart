import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/screens/account/widgets/client_selection_widget.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/circular_loader.dart';

class FundProfilePage extends StatelessWidget {
  const FundProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthUnauthenticated) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            '/',
            (Route<dynamic> route) => false,
          ); // remove all previous routes);
        }
      },
      builder: (context, state) {
        if (state is! AuthAuthenticated) {
          return const Scaffold(
            backgroundColor: ThemeConstants.backgroundColor,
            body: Center(child: CircularLoader()),
          );
        }
        final user = state.credentialsModel;

        return Scaffold(
          backgroundColor: ThemeConstants.backgroundColor,
          appBar: AppBar(
            backgroundColor: ThemeConstants.backgroundColor,
            title: Row(
              spacing: 6,
              children: [
                Text(
                  "Profile",
                  style: TextStyle(
                    color: const Color(0xffCDCDCD),
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            
            iconTheme: IconThemeData(color: Color(0xffCDCDCD)),
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundImage: NetworkImage(user.pictureUrl),
                        ),
                        SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                'Hi, ${user.name.split("@").first.toCapitalized}',
                                style: TextStyle(
                                  color: Color(0xffcdcdcd),
                                  fontSize: 18, // Initial size
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ),
                            Text(
                              user.email,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                            Text(
                              "Client Id ${user.clientId.toString()}",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24),

                  ClientSelectionWidget(userClientId: user.clientId),

                  Spacer(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
