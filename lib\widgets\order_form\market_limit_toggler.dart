import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;

import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:phoenix/utils/theme_constants.dart';

class MarketLimitToggler extends StatelessWidget {
  final bool isMarket;
  final VoidCallback onMarketTap;
  final VoidCallback onLimitTap;

  const MarketLimitToggler({
    Key? key,
    required this.isMarket,
    required this.onMarketTap,
    required this.onLimitTap,
  }) : super(key: key);

  Widget _buildToggleButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      height: 40,
      width: 100,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color:
                isSelected ? const Color(0xff1C1C1C) : const Color(0xff555555),
            borderRadius: BorderRadius.circular(50),
          ),
          child: Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 20,
            ),
          ),
        ),
      ),
    );
  }
  

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 58,
      width: 380,
      decoration: BoxDecoration(
        color: const Color(0xFF3F4651),
        borderRadius: BorderRadius.circular(15),
        boxShadow: ThemeConstants.neomorpicShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: 13,
        children: [
          const SizedBox(width: 1),
          _buildToggleButton(
            label: "Market",
            isSelected: isMarket,
            onTap: onMarketTap,
          ),
          _buildToggleButton(
            label: "Limit",
            isSelected: !isMarket,
            onTap: onLimitTap,
          ),
        ],
      ),
    );
  }
}
