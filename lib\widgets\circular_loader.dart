import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class CircularLoader extends StatelessWidget {
  const CircularLoader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return const CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation(ThemeConstants.blue),
      strokeWidth: 3,
      strokeCap: StrokeCap.square,
    );
  }
}
