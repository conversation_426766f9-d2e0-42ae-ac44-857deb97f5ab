import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';

import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/symbols/market_trend_arrow.dart';
import 'package:phoenix/widgets/symbols/market_trend_arrow2.dart';

class PnlBottomBar extends StatefulWidget {
  final double invested;
  final double current;
  final double daysPnL;
  final double daysPnLPercentage;
  final double changeAmount;
  final double changePercentage;
  final VoidCallback? onTap;

  const PnlBottomBar({
    super.key,
    required this.invested,
    required this.current,
    required this.daysPnL,
    required this.daysPnLPercentage,
    required this.changeAmount,
    required this.changePercentage,
    this.onTap,
  });

  @override
  State<PnlBottomBar> createState() => _PnlBottomBarState();
}

class _PnlBottomBarState extends State<PnlBottomBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _heightAnimation = Tween<double>(begin: 40, end: 140).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpand() {
    setState(() {
      //_isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });

    if (widget.onTap != null) {
      widget.onTap!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isPositiveChange = widget.changeAmount >= 0;
    final isPositiveChangePercentage = widget.changePercentage >= 0;
    final isPositiveDayPnL = widget.daysPnL >= 0;
    final isPositiveDayPnLPercentage = widget.daysPnLPercentage >= 0;
    final isCurrentPositive = widget.current >= widget.invested;

    return GestureDetector(
      onTap: _toggleExpand,
      child: Container(
        height: _heightAnimation.value,
        width: double.infinity,
        //call insets box decoration
        decoration: BoxDecoration(
          color:
              _isExpanded ? const Color(0xff24272c) : const Color(0xff555555),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
          boxShadow: ThemeConstants.innerShadow,
        ),

        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.end, // Align content to bottom
            mainAxisSize: MainAxisSize.min,
            children: [
              // Portfolio Net Worth section (expandable upward)
              if (_heightAnimation.value > 75)
                Opacity(
                  opacity: ((_heightAnimation.value - 40) /
                      100), // Fade in as it expands
                  child: Container(
                    padding: EdgeInsets.only(left: 20, right: 20, top: 10),
                    
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(25),
                        topRight: Radius.circular(300),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          "Portfolio's Net Worth",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Invested
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  "Invested",
                                  style: TextStyle(
                                    color: ThemeConstants.netWorthHeaderColor,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.currency_exchange_rounded,
                                      color: ThemeConstants.netWorthGreenColor,
                                      size: 14,
                                    ),
                                    SizedBox(width: 4),
                                    Text(
                                      UtilFunctions.formatIndianCurrency(
                                          widget.invested),
                                      style: const TextStyle(
                                        color:
                                            ThemeConstants.netWorthAmountColor,
                                        fontSize: 15,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            // Change i.e center column
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Row(
                                  children: [
                                    MarketTrendArrow(
                                      isPositiveChange: isPositiveChange,
                                    ),
                                    priceFormatter(
                                      price: widget.changeAmount,
                                      fontSize: 13,
                                    ),
                                  ],
                                ),
                                Text(
                                  "${isPositiveChangePercentage ? '+' : ''}${widget.changePercentage.toStringAsFixed(2)}%",
                                  style: TextStyle(
                                    color: isPositiveChangePercentage
                                        ? ThemeConstants.netWorthGreenColor
                                        : ThemeConstants.netWorthTextRedColor,
                                    fontSize: 13,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),

                            // Current
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  "Current",
                                  style: TextStyle(
                                    color: ThemeConstants.netWorthHeaderColor,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                Row(
                                  children: [
                                    MarketTrendArrow(
                                      isPositiveChange: isCurrentPositive,
                                    ),
                                    priceFormatter(
                                      price: widget.current,
                                      fontSize: 15,
                                      isTrendPassed: true,
                                      trend: isCurrentPositive,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(
                          color: Colors.white,
                          thickness: 1,
                        ),
                      ],
                    ),
                  ),
                ),

              // Always visible part - Day's P&L (at the bottom)
              Container(
                height: 28.5,
                alignment: Alignment.bottomCenter,
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      "P&L",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Row(
                      children: [
                        MarketTrendArrow2(isPositiveChange: isPositiveDayPnL),
                        priceFormatter(price: widget.daysPnL, fontSize: 15),
                      ],
                    ),
                    Text(
                      "${isPositiveDayPnLPercentage ? '+' : ''}${widget.daysPnLPercentage.toStringAsFixed(2)}",
                      style: TextStyle(
                        color: isPositiveDayPnLPercentage
                            ? ThemeConstants.netWorthGreenColor
                            : ThemeConstants.netWorthTextRedColor,
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Text priceFormatter({double price = 0, double fontSize = 13, bool trend = false, bool isTrendPassed = false}) {
    Color getColorForValue(double val) {
      if(isTrendPassed) return trend ? ThemeConstants.netWorthGreenColor : ThemeConstants.netWorthTextRedColor;
      if (val > 0) return ThemeConstants.netWorthGreenColor;
      if (val < 0) return ThemeConstants.netWorthTextRedColor;
      return ThemeConstants.netWorthAmountColor;
    }

    return Text(
      " ${UtilFunctions.formatIndianCurrency(price)}",
      style: TextStyle(
        color: getColorForValue(price),
        fontSize: fontSize,
        fontWeight: FontWeight.w700,
      ),
    );
  }
}
