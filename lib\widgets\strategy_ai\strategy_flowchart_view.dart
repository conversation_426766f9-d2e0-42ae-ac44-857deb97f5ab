import 'dart:convert';
import 'package:flutter/material.dart';
import '../../utils/theme_constants.dart';

class StrategyFlowchartView extends StatelessWidget {
  final String jsonText;

  const StrategyFlowchartView({super.key, required this.jsonText});

  @override
  Widget build(BuildContext context) {
    Map<String, dynamic> strategyData;
    try {
      strategyData = jsonDecode(jsonText);
    } catch (e) {
      return _buildErrorView(context);
    }

    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: ThemeConstants.backgroundColor,
        elevation: 0,
        centerTitle: true,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_tree, color: ThemeConstants.blue, size: 28),
            SizedBox(width: 8),
            Text(
              'Strategy Flowchart',
              style: TextStyle(
                color: ThemeConstants.zenWhite,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ],
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new_rounded, color: ThemeConstants.zenWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              ThemeConstants.backgroundColor,
              ThemeConstants.zenBlack1.withOpacity(0.7)
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20),
          child: Column(
            children: [
              _buildUnderlyingSection(strategyData),
              SizedBox(height: 20),
              _buildArrow(),
              SizedBox(height: 20),
              _buildEntryConditionSection(strategyData),
              SizedBox(height: 20),
              _buildArrow(),
              SizedBox(height: 20),
              _buildExitConditionSection(strategyData),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: ThemeConstants.backgroundColor,
        elevation: 0,
        title: Text('Error', style: TextStyle(color: ThemeConstants.zenWhite)),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new_rounded, color: ThemeConstants.zenWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'Invalid JSON Format',
              style: TextStyle(
                color: ThemeConstants.zenWhite,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Unable to parse the strategy JSON',
              style: TextStyle(
                color: ThemeConstants.zenWhite.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnderlyingSection(Map<String, dynamic> strategyData) {
    final underlying = strategyData['underlying'] as List<dynamic>?;
    return _buildCard(
      title: 'Underlying Assets',
      icon: Icons.trending_up,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (underlying != null && underlying.isNotEmpty)
            ...underlying.map((asset) => Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.fiber_manual_record, size: 8, color: ThemeConstants.blue),
                      SizedBox(width: 8),
                      Text(
                        asset.toString(),
                        style: TextStyle(
                          color: ThemeConstants.zenWhite,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ))
          else
            Text(
              'No underlying assets specified',
              style: TextStyle(
                color: ThemeConstants.zenWhite.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEntryConditionSection(Map<String, dynamic> strategyData) {
    final entryCondition = strategyData['entryCondition'] as Map<String, dynamic>?;
    return _buildConditionCard(
      title: 'Entry Condition',
      icon: Icons.login,
      iconColor: Colors.green,
      condition: entryCondition,
      actionType: 'ENTRY',
    );
  }

  Widget _buildExitConditionSection(Map<String, dynamic> strategyData) {
    final exitCondition = strategyData['exitCondition'] as Map<String, dynamic>?;
    return _buildConditionCard(
      title: 'Exit Condition',
      icon: Icons.logout,
      iconColor: Colors.red,
      condition: exitCondition,
      actionType: 'EXIT',
    );
  }

  Widget _buildConditionCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required Map<String, dynamic>? condition,
    required String actionType,
  }) {
    return _buildCard(
      title: title,
      icon: icon,
      iconColor: iconColor,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (condition != null) ...[
            _buildConditionDetails(condition),
            SizedBox(height: 16),
            _buildActionDetails(condition['content']?['action'] as Map<String, dynamic>?),
          ] else
            Text(
              'No $actionType condition specified',
              style: TextStyle(
                color: ThemeConstants.zenWhite.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildConditionDetails(Map<String, dynamic> condition) {
    final content = condition['content'] as Map<String, dynamic>?;
    if (content == null) return SizedBox();

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeConstants.zenBlack1.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: ThemeConstants.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.rule, size: 16, color: ThemeConstants.blue),
              SizedBox(width: 8),
              Text(
                'Condition Logic',
                style: TextStyle(
                  color: ThemeConstants.blue,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Match: ${content['shouldMatch'] ?? 'N/A'}',
            style: TextStyle(
              color: ThemeConstants.zenWhite,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 8),
          _buildConditionsList(content['conditions'] as List<dynamic>?),
        ],
      ),
    );
  }

  Widget _buildConditionsList(List<dynamic>? conditions) {
    if (conditions == null || conditions.isEmpty) {
      return Text(
        'No conditions specified',
        style: TextStyle(
          color: ThemeConstants.zenWhite.withOpacity(0.7),
          fontSize: 12,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: conditions.map((condition) {
        final content = condition['content'] as Map<String, dynamic>?;
        final actualCondition = content?['actualCondition'] as Map<String, dynamic>?;
        
        if (actualCondition == null) return SizedBox();

        return Container(
          margin: EdgeInsets.only(top: 8),
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: ThemeConstants.zenBlack.withOpacity(0.5),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              Icon(Icons.access_time, size: 14, color: Colors.orange),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${actualCondition['type']?.toString().toUpperCase() ?? 'UNKNOWN'}: ${actualCondition['timeValue'] ?? 'N/A'}',
                  style: TextStyle(
                    color: ThemeConstants.zenWhite,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActionDetails(Map<String, dynamic>? action) {
    if (action == null) return SizedBox();

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeConstants.zenBlack1.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.play_arrow, size: 16, color: Colors.orange),
              SizedBox(width: 8),
              Text(
                'Action Details',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          _buildActionItem('Order Type', action['orderType']),
          _buildActionItem('Transaction', action['transactionType']),
          _buildActionItem('Quantity', action['qty']),
          _buildActionItem('Product Type', action['productType']),
        ],
      ),
    );
  }

  Widget _buildActionItem(String label, dynamic value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          SizedBox(width: 16),
          Icon(Icons.arrow_right, size: 12, color: ThemeConstants.zenWhite.withOpacity(0.7)),
          SizedBox(width: 4),
          Text(
            '$label: ',
            style: TextStyle(
              color: ThemeConstants.zenWhite.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
          Text(
            value?.toString().toUpperCase() ?? 'N/A',
            style: TextStyle(
              color: ThemeConstants.zenWhite,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({
    required String title,
    required IconData icon,
    Color? iconColor,
    required Widget content,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: ThemeConstants.zenBlack.withOpacity(0.4),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: iconColor ?? ThemeConstants.blue, size: 24),
                SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    color: ThemeConstants.zenWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildArrow() {
    return Column(
      children: [
        Container(
          width: 2,
          height: 20,
          color: ThemeConstants.blue.withOpacity(0.5),
        ),
        Icon(
          Icons.keyboard_arrow_down,
          color: ThemeConstants.blue,
          size: 32,
        ),
      ],
    );
  }
}