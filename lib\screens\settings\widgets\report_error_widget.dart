import 'package:flutter/material.dart';
import 'package:phoenix/services/logger_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';
import 'package:phoenix/widgets/toast/toast_utils.dart';

class ReportErrorWidget extends StatelessWidget {
  const ReportErrorWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
        color: ThemeConstants.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: ThemeConstants.neomorpicShadow,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.bug_report_outlined,
                  color: ThemeConstants.zenWhite,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  "Error Reporting",
                  style: TextStyle(
                    color: ThemeConstants.zenWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              "If you encounter any issues with the app, you can share the debug logs to help us diagnose and fix the problem.",
              style: TextStyle(
                color: ThemeConstants.zenGrey,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildReportButton(
                  context,
                  "Current Log",
                  Icons.file_present_outlined,
                  _shareCurrentLog,
                ),
                const SizedBox(width: 16),
                _buildReportButton(
                  context,
                  "All Logs",
                  Icons.folder_outlined,
                  _shareAllLogs,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportButton(
    BuildContext context,
    String label,
    IconData icon,
    Function(BuildContext) onTap,
  ) {
    return Expanded(
      child: InkWell(
        onTap: () => onTap(context),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: ThemeConstants.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: ThemeConstants.blue.withOpacity(0.2),
                offset: const Offset(0, 0),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: ThemeConstants.blue,
                size: 24,
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: ThemeConstants.zenWhite,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _shareCurrentLog(BuildContext context) async {
    try {
      final loggerService = LoggerService();
      await loggerService.shareCurrentLog(context);
      ToastUtil.showToast(
        context,
        "Log file ready to share",
        ToastType.success,
      );
    } catch (e) {
      ToastUtil.showToast(
        context,
        "Failed to share log: $e",
        ToastType.error,
      );
    }
  }

  void _shareAllLogs(BuildContext context) async {
    try {
      final loggerService = LoggerService();
      await loggerService.shareAllLogs(context);
      ToastUtil.showToast(
        context,
        "Log files ready to share",
        ToastType.success,
      );
    } catch (e) {
      ToastUtil.showToast(
        context,
        "Failed to share logs: $e",
        ToastType.error,
      );
    }
  }
}