import 'package:flutter/material.dart';

class CancelOrderDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const CancelOrderDialog({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Delete Order"),
      content: const Text("Are you sure you want to cancel this order? This action cannot be undone."),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text("No"),
        ),
        TextButton(
          onPressed: () {
            onConfirm();
            Navigator.of(context).pop();
          },
          child: const Text("Yes", style: TextStyle(color: Colors.red)),
        ),
      ],
    );
  }
}
