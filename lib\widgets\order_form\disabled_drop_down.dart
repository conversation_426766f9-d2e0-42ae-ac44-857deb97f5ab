import 'package:flutter/material.dart';

class DisabledDropdown extends StatelessWidget {
  final String value;

  const DisabledDropdown({
    super.key,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      height: 40,
      width: MediaQuery.of(context).size.width * 0.445,
      decoration: BoxDecoration(
        color: const Color(0xff353535),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Text(
          value,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontSize: 15,
            color: Color(0xffADADAD),
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
