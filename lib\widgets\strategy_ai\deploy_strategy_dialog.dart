import 'package:flutter/material.dart';
import '../../utils/theme_constants.dart';

class DeployStrategyDialog extends StatelessWidget {
  final TextEditingController controller;
  final ValueNotifier<bool> isValid;
  final String strategyName;
  final void Function(String) onChanged;
  final void Function() onCancel;
  final void Function() onDeploy;
  final bool isDeploying;

  const DeployStrategyDialog({
    super.key,
    required this.controller,
    required this.isValid,
    required this.strategyName,
    required this.onChanged,
    required this.onCancel,
    required this.onDeploy,
    this.isDeploying = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        width: 400,
        decoration: BoxDecoration(
          color: ThemeConstants.backgroundColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: ThemeConstants.neomorpicShadow,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: const BoxDecoration(
                color: Color(0xFF23272f),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: const Center(
                child: Text(
                  'Deploy Strategy',
                  style: TextStyle(
                    color: ThemeConstants.zenWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Name your strategy',
                    style: TextStyle(
                      color: ThemeConstants.zenWhite,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ValueListenableBuilder<bool>(
                    valueListenable: isValid,
                    builder: (context, valid, _) {
                      return Container(
                        height: 56,
                        decoration: BoxDecoration(
                          color: const Color(0xff353535),
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              offset: const Offset(2, 2),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        child: TextField(
                          controller: controller,
                          autofocus: true,
                          style: const TextStyle(
                            color: ThemeConstants.zenWhite,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Enter strategy name',
                            hintStyle: const TextStyle(
                              color: Color(0xffADADAD),
                              fontSize: 16,
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                            border: InputBorder.none,
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: const BorderSide(
                                color: ThemeConstants.blue,
                                width: 1.5,
                              ),
                            ),
                          ),
                          onChanged: onChanged,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                  // Description
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: ThemeConstants.zenBlack.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: const [
                        Icon(Icons.info_outline, color: ThemeConstants.blue, size: 20),
                        SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Strategy name can include A-Z, a-z, 0-9, and _',
                            style: TextStyle(
                              color: ThemeConstants.zenGrey,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: const BoxDecoration(
                color: Color(0xFF1D1F23),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Cancel button
                  TextButton(
                    onPressed: onCancel,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(
                        color: ThemeConstants.zenGrey,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Deploy button
                  ValueListenableBuilder<bool>(
                    valueListenable: isValid,
                    builder: (context, valid, _) {
                      return ElevatedButton(
                        onPressed: valid && !isDeploying ? onDeploy : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ThemeConstants.blue,
                          foregroundColor: Colors.white,
                          disabledBackgroundColor: ThemeConstants.blue.withOpacity(0.3),
                          disabledForegroundColor: Colors.white.withOpacity(0.5),
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: const [
                            Icon(Icons.cloud_upload, size: 18),
                            SizedBox(width: 8),
                            Text(
                              'Deploy',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
