import 'package:flutter/material.dart';
import 'package:phoenix/features/authentication/data/data_provider/biometric_service.dart';

class BiometricToggle extends StatefulWidget {
  const BiometricToggle({Key? key}) : super(key: key);

  @override
  State<BiometricToggle> createState() => _BiometricToggleState();
}

class _BiometricToggleState extends State<BiometricToggle> {
  final BiometricService _biometricService = BiometricService();
  bool _isEnabled = false;
  bool _isAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkBiometrics();
  }

  Future<void> _checkBiometrics() async {
    final available = await _biometricService.isBiometricsAvailable();
    final enabled = await _biometricService.isBiometricEnabled();
    setState(() {
      _isAvailable = available;
      _isEnabled = enabled;
    });
  }

  Future<void> _toggleBiometric(bool value) async {
    try {
      // If enabling biometrics
      if (value) {
        final authenticated = await _biometricService.authenticate();
        if (!authenticated) {
          // If authentication fails or is canceled, keep it disabled
          await _biometricService.setBiometricEnabled(false);
          setState(() => _isEnabled = false);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Biometric authentication failed or was canceled.'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }
      
      // Only reach here if authentication succeeded or we're disabling biometrics
      await _biometricService.setBiometricEnabled(value);
      setState(() => _isEnabled = value);
    } catch (e) {
      setState(() => _isEnabled = false); // Ensure toggle is reset on error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: const Text(
        'Enable Biometric Authentication',
        style: TextStyle(color: Color(0xffCDCDCD)),
      ),
      subtitle: !_isAvailable 
        ? const Text(
            'Biometrics not available on this device',
            style: TextStyle(color: Colors.red),
          )
        : null,
      trailing: Switch(
        value: _isEnabled,
        onChanged: _isAvailable ? _toggleBiometric : null,
      ),
    );
  }
}

