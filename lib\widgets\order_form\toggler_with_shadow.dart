import 'package:flutter/material.dart';

class TogglerWithShadow extends StatelessWidget {
  final bool isEQ;
  final String title1;
  final String title2;
  final void Function() action1;
  final void Function() action2;

  const TogglerWithShadow({
    super.key,
    required this.title1,
    required this.title2,
    required this.isEQ,
    required this.action1,
    required this.action2,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8),
      height: 40,
      width: MediaQuery.of(context).size.width * 0.4,
      decoration: BoxDecoration(
        color: Color(0xff3F4651).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(0xff3F4651),
            blurRadius: 10,
            spreadRadius: 2,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          _buildToggleItem(
            text: title1,
            isSelected: isEQ,
            onTap: () => {action1()},
            context: context
          ),
          _buildToggleItem(
            text: title2,
            isSelected: !isEQ,
            onTap: () => {action2()},
            context: context
          ),
        ],
      ),
    );
  }

  Widget _buildToggleItem({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
    required BuildContext context,
  }) {
    return SizedBox(
      height: 30,
      width: MediaQuery.of(context).size.width * 0.2,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Color(0xff1C1C1C),
                      blurRadius: 3,
                      spreadRadius: 1,
                    ),
                  ]
                : [],
          ),
          child: Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 20,
            ),
          ),
        ),
      ),
    );
  }
}
