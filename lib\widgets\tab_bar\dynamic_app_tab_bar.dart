import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class TabData {
  final String title;
  final int count;

  TabData({required this.title, required this.count});
}

class DynamicAppTabBar extends StatelessWidget {
  final List<TabData> tabs;
  final TabController? controller;
  final bool showCount;

  const DynamicAppTabBar({
    super.key,
    required this.tabs,
    this.controller,
    this.showCount = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.90,
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      alignment: Alignment.center,
      child: TabBar(
        controller: controller,
        isScrollable: true,
        indicatorColor: ThemeConstants.blue,
        indicatorPadding: const EdgeInsets.symmetric(vertical: -1),
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(width: 1.0, color: ThemeConstants.blue),
          insets: EdgeInsets.only(
            right: 30.0,
            left: 20.0,
          ),
        ),
        labelColor: ThemeConstants.blue,
        unselectedLabelColor: Colors.white,
        dividerColor: Colors.transparent,
        labelPadding: const EdgeInsets.symmetric(horizontal: 16.0),
        tabs: tabs.map((tabData) {
          return Container(
            height: 32,
            child: Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    fit: FlexFit.loose,
                    child: Text(
                      tabData.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  if (showCount) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: ThemeConstants.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        "${tabData.count}",
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: ThemeConstants.blue,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}