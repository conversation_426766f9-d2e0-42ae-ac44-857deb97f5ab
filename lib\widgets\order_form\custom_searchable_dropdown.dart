import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/utils/theme_constants.dart';

class CustomSearchableDropdown extends StatefulWidget {
  final String type;
  final ValueChanged<SecurityModel?> onSelected;
  final String hintText;

  CustomSearchableDropdown({
    required this.onSelected,
    this.hintText = 'Select an item',
    required this.type,
  });

  @override
  _CustomSearchableDropdownState createState() =>
      _CustomSearchableDropdownState();
}

class _CustomSearchableDropdownState extends State<CustomSearchableDropdown> {
  //SecurityModel selectedValue = SecurityModel(zenId: 90360, tradingSymbol: "TCS", strike: 0.0, exchange: "NSE", lotSize: 1, instrumentType: "EQ");

  SecurityModel? selectedValue;
  void _navigateToSearchPage() async {
    final SecurityModel? result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SearchPage(type: widget.type),
      ),
    );

    if (result != null) {
      setState(() {
        selectedValue = result;
      });
      widget.onSelected(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: _navigateToSearchPage,
      child: Container(
        padding: const EdgeInsets.all(2),
        alignment: Alignment.center,
        height: 40,
        width: 205,
        decoration: BoxDecoration(
          color: const Color(0xff353535),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              selectedValue == null
                  ? widget.hintText
                  : selectedValue!.tradingSymbol,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 16,
                color: Color(0xffADADAD),
                fontWeight: FontWeight.w400,
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Color(0xffADADAD),
            ),
          ],
        ),
      ),
    );
  }
}

//Search page
class SearchPage extends StatefulWidget {
  final SecurityModel? selectedValue;
  final String type;

  SearchPage({this.selectedValue, required this.type});

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  String searchQuery = '';
  List<SecurityModel> filteredItems = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: ThemeConstants.backgroundColor,
        foregroundColor: ThemeConstants.tileIconColor,
        title: TextField(
          autofocus: true,
          style: TextStyle(
              color: ThemeConstants.tileIconColor,
              fontFamily: 'CupertinoSystemText',
              fontSize: 18),
          decoration: InputDecoration(
            hintText: 'Search...',
            border: InputBorder.none,
          ),
          onChanged: _filterItems,
        ),
      ),
      body: BlocBuilder<SecurityListBloc, SecurityListState>(
        builder: (context, state) {
          if (state is SecurityListLoaded) {
            // Initialize filteredItems based on the type
            final List<SecurityModel> items =
                widget.type == 'equity' ? state.equityList : state.featuresList;
            // Initialize filteredItems with all securities when the state is loaded
            if (filteredItems.isEmpty) {
              filteredItems = items;
            }

            return ListView.builder(
              itemCount: filteredItems.length,
              itemBuilder: (context, index) {
                final security = filteredItems[index];
                return ListTile(
                  titleAlignment: ListTileTitleAlignment.center,
                  title: Text(
                    security.tradingSymbol,
                    style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'CupertinoSystemText',
                        fontWeight: FontWeight.w400),
                  ),
                  onTap: () {
                    // Return the selected SecurityModel to the caller
                    Navigator.of(context).pop(security);
                  },
                );
              },
            );
          } else if (state is SecurityListLoading) {
            return Center(child: CircularProgressIndicator());
          } else if (state is SecurityListError) {
            return Center(child: Text('Error loading securities'));
          } else {
            return Center(child: Text('No data available'));
          }
        },
      ),
    );
  }

  void _filterItems(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        // If the query is empty, show all items
        // If the query is empty, show all items
        final state =
            context.read<SecurityListBloc>().state as SecurityListLoaded;
        filteredItems =
            widget.type == 'equity' ? state.equityList : state.featuresList;
      } else {
        // Filter the list based on tradingSymbol
        final state =
            context.read<SecurityListBloc>().state as SecurityListLoaded;
        final items =
            widget.type == 'equity' ? state.equityList : state.featuresList;
        filteredItems = items
            .where((security) => security.tradingSymbol
                .toLowerCase()
                .contains(query.toLowerCase()))
            .toList();
      }
    });
  }
}
